<?php
/**
 * Lindenhof Admin Settings Page
 * 
 * @package Lindenhof
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Add admin menu
add_action('admin_menu', 'lindenhof_add_admin_menu');
function lindenhof_add_admin_menu() {
    add_options_page(
        __('Lindenhof Instellingen', 'lindenhof'),
        __('Lindenhof', 'lindenhof'),
        'manage_options',
        'lindenhof-settings',
        'lindenhof_settings_page'
    );
}

// Register settings
add_action('admin_init', 'lindenhof_settings_init');
function lindenhof_settings_init() {
    // Register settings
    register_setting('lindenhof_settings', 'lindenhof_huurkalender_api_key');
    register_setting('lindenhof_settings', 'lindenhof_property_id');
    register_setting('lindenhof_settings', 'lindenhof_google_api_key');
    register_setting('lindenhof_settings', 'lindenhof_allow_core_blocks');
    register_setting('lindenhof_settings', 'lindenhof_contact_email');
    register_setting('lindenhof_settings', 'lindenhof_contact_phone');
    register_setting('lindenhof_settings', 'lindenhof_contact_address');
    register_setting('lindenhof_settings', 'lindenhof_social_facebook');
    register_setting('lindenhof_settings', 'lindenhof_social_instagram');
    register_setting('lindenhof_settings', 'lindenhof_social_youtube');
    register_setting('lindenhof_settings', 'lindenhof_accommodatie_locatie');
    register_setting('lindenhof_settings', 'lindenhof_accommodatie_personen');
    register_setting('lindenhof_settings', 'lindenhof_accommodatie_slaapplaatsen');
    register_setting('lindenhof_settings', 'lindenhof_logo');
    register_setting('lindenhof_settings', 'lindenhof_logo_white');
    register_setting('lindenhof_settings', 'lindenhof_whatsapp_number');
    
    // Add settings sections
    add_settings_section(
        'lindenhof_api_section',
        __('API Instellingen', 'lindenhof'),
        'lindenhof_api_section_callback',
        'lindenhof_settings'
    );
    
    add_settings_section(
        'lindenhof_contact_section',
        __('Contact Informatie', 'lindenhof'),
        'lindenhof_contact_section_callback',
        'lindenhof_settings'
    );
    
    add_settings_section(
        'lindenhof_social_section',
        __('Social Media', 'lindenhof'),
        'lindenhof_social_section_callback',
        'lindenhof_settings'
    );

    // logo
    add_settings_section(
        'lindenhof_logo_section',
        __('Logo', 'lindenhof'),
        'lindenhof_logo_section_callback',
        'lindenhof_settings'
    );

    add_settings_section(
        'lindenhof_accommodatie_section',
        __('Accommodatie Informatie', 'lindenhof'),
        'lindenhof_accommodatie_section_callback',
        'lindenhof_settings'
    );
    
    // Add settings fields
    add_settings_field(
        'lindenhof_huurkalender_api_key',
        __('Huurkalender.nl API Key', 'lindenhof'),
        'lindenhof_api_key_render',
        'lindenhof_settings',
        'lindenhof_api_section'
    );

    add_settings_field(
        'lindenhof_property_id',
        __('Property ID', 'lindenhof'),
        'lindenhof_property_id_render',
        'lindenhof_settings',
        'lindenhof_api_section'
    );

    add_settings_field(
        'lindenhof_google_api_key',
        __('Google API Key', 'lindenhof'),
        'lindenhof_google_api_key_render',
        'lindenhof_settings',
        'lindenhof_api_section'
    );

    add_settings_field(
        'lindenhof_allow_core_blocks',
        __('Core blocks toestaan', 'lindenhof'),
        'lindenhof_allow_core_blocks_render',
        'lindenhof_settings',
        'lindenhof_api_section'
    );
    
    add_settings_field(
        'lindenhof_contact_email',
        __('E-mailadres', 'lindenhof'),
        'lindenhof_contact_email_render',
        'lindenhof_settings',
        'lindenhof_contact_section'
    );
    
    add_settings_field(
        'lindenhof_contact_phone',
        __('Telefoonnummer', 'lindenhof'),
        'lindenhof_contact_phone_render',
        'lindenhof_settings',
        'lindenhof_contact_section'
    );
    
    add_settings_field(
        'lindenhof_contact_address',
        __('Adres', 'lindenhof'),
        'lindenhof_contact_address_render',
        'lindenhof_settings',
        'lindenhof_contact_section'
    );
    
    add_settings_field(
        'lindenhof_social_facebook',
        __('Facebook URL', 'lindenhof'),
        'lindenhof_social_facebook_render',
        'lindenhof_settings',
        'lindenhof_social_section'
    );
    
    add_settings_field(
        'lindenhof_social_instagram',
        __('Instagram URL', 'lindenhof'),
        'lindenhof_social_instagram_render',
        'lindenhof_settings',
        'lindenhof_social_section'
    );
    
    add_settings_field(
        'lindenhof_social_youtube',
        __('YouTube URL', 'lindenhof'),
        'lindenhof_social_youtube_render',
        'lindenhof_settings',
        'lindenhof_social_section'
    );

    add_settings_field(
        'lindenhof_accommodatie_locatie',
        __('Locatie', 'lindenhof'),
        'lindenhof_accommodatie_locatie_render',
        'lindenhof_settings',
        'lindenhof_accommodatie_section'
    );

    add_settings_field(
        'lindenhof_accommodatie_personen',
        __('Aantal personen', 'lindenhof'),
        'lindenhof_accommodatie_personen_render',
        'lindenhof_settings',
        'lindenhof_accommodatie_section'
    );

    add_settings_field(
        'lindenhof_accommodatie_slaapplaatsen',
        __('Aantal slaapplaatsen', 'lindenhof'),
        'lindenhof_accommodatie_slaapplaatsen_render',
        'lindenhof_settings',
        'lindenhof_accommodatie_section'
    );

    add_settings_field(
        'lindenhof_logo',
        __('Logo (normaal)', 'lindenhof'),
        'lindenhof_logo_render',
        'lindenhof_settings',
        'lindenhof_logo_section'
    );

    add_settings_field(
        'lindenhof_logo_white',
        __('Logo (wit)', 'lindenhof'),
        'lindenhof_logo_white_render',
        'lindenhof_settings',
        'lindenhof_logo_section'
    );

    add_settings_field(
        'lindenhof_whatsapp_number',
        __('WhatsApp Nummer', 'lindenhof'),
        'lindenhof_whatsapp_number_render',
        'lindenhof_settings',
        'lindenhof_contact_section'
    );
}

// Section callbacks
function lindenhof_api_section_callback() {
    echo '<p>' . esc_html__('Configureer hier de API instellingen voor externe diensten en editor beperkingen.', 'lindenhof') . '</p>';
}

function lindenhof_contact_section_callback() {
    echo '<p>' . esc_html__('Voer hier de contact informatie in die op de website wordt getoond.', 'lindenhof') . '</p>';
}

function lindenhof_social_section_callback() {
    echo '<p>' . esc_html__('Voer hier de social media links in.', 'lindenhof') . '</p>';
}

function lindenhof_accommodatie_section_callback() {
    echo '<p>' . esc_html__('Voer hier de accommodatie informatie in die wordt getoond in de header.', 'lindenhof') . '</p>';
}

function lindenhof_logo_section_callback() {
    echo '<p>' . esc_html__('Upload hier het logo dat in de header wordt getoond.', 'lindenhof') . '</p>';
}

// Field render functions
function lindenhof_api_key_render() {
    $value = get_option('lindenhof_huurkalender_api_key', '');
    echo '<input type="password" name="lindenhof_huurkalender_api_key" value="' . esc_attr($value) . '" class="regular-text" />';
    echo '<p class="description">' . esc_html__('API key voor communicatie met huurkalender.nl', 'lindenhof') . '</p>';
}

function lindenhof_property_id_render() {
    $value = get_option('lindenhof_property_id', '');
    echo '<input type="text" name="lindenhof_property_id" value="' . esc_attr($value) . '" class="regular-text" />';
    echo '<p class="description">' . esc_html__('Property ID van uw accommodatie bij huurkalender.nl', 'lindenhof') . '</p>';
}

function lindenhof_google_api_key_render() {
    $value = get_option('lindenhof_google_api_key', '');
    echo '<input type="password" name="lindenhof_google_api_key" value="' . esc_attr($value) . '" class="regular-text" />';
    echo '<p class="description">' . esc_html__('Google API key voor Google Reviews en Places API', 'lindenhof') . '</p>';
}

function lindenhof_allow_core_blocks_render() {
    $value = get_option('lindenhof_allow_core_blocks', false);
    echo '<label>';
    echo '<input type="checkbox" name="lindenhof_allow_core_blocks" value="1" ' . checked(1, $value, false) . ' />';
    echo ' ' . esc_html__('Sta basis WordPress blocks toe (paragraaf, kop, lijst, etc.)', 'lindenhof');
    echo '</label>';
    echo '<p class="description">' . esc_html__('Indien uitgeschakeld kunnen alleen Lindenhof custom blocks gebruikt worden.', 'lindenhof') . '</p>';
}

function lindenhof_contact_email_render() {
    $value = get_option('lindenhof_contact_email', '');
    echo '<input type="email" name="lindenhof_contact_email" value="' . esc_attr($value) . '" class="regular-text" />';
}

function lindenhof_contact_phone_render() {
    $value = get_option('lindenhof_contact_phone', '');
    echo '<input type="tel" name="lindenhof_contact_phone" value="' . esc_attr($value) . '" class="regular-text" />';
}

function lindenhof_contact_address_render() {
    $value = get_option('lindenhof_contact_address', '');
    echo '<textarea name="lindenhof_contact_address" rows="3" class="large-text">' . esc_textarea($value) . '</textarea>';
}

function lindenhof_social_facebook_render() {
    $value = get_option('lindenhof_social_facebook', '');
    echo '<input type="url" name="lindenhof_social_facebook" value="' . esc_attr($value) . '" class="regular-text" />';
}

function lindenhof_social_instagram_render() {
    $value = get_option('lindenhof_social_instagram', '');
    echo '<input type="url" name="lindenhof_social_instagram" value="' . esc_attr($value) . '" class="regular-text" />';
}

function lindenhof_social_youtube_render() {
    $value = get_option('lindenhof_social_youtube', '');
    echo '<input type="url" name="lindenhof_social_youtube" value="' . esc_attr($value) . '" class="regular-text" />';
}

function lindenhof_accommodatie_locatie_render() {
    $value = get_option('lindenhof_accommodatie_locatie', '');
    echo '<input type="text" name="lindenhof_accommodatie_locatie" value="' . esc_attr($value) . '" class="regular-text" />';
    echo '<p class="description">' . esc_html__('Locatie van de accommodatie (bijv. "Geijsteren, Nederland")', 'lindenhof') . '</p>';
}

function lindenhof_accommodatie_personen_render() {
    $value = get_option('lindenhof_accommodatie_personen', '');
    echo '<input type="number" name="lindenhof_accommodatie_personen" value="' . esc_attr($value) . '" class="small-text" min="1" max="50" />';
    echo '<p class="description">' . esc_html__('Maximum aantal personen dat kan verblijven', 'lindenhof') . '</p>';
}

function lindenhof_accommodatie_slaapplaatsen_render() {
    $value = get_option('lindenhof_accommodatie_slaapplaatsen', '');
    echo '<input type="number" name="lindenhof_accommodatie_slaapplaatsen" value="' . esc_attr($value) . '" class="small-text" min="1" max="30" />';
    echo '<p class="description">' . esc_html__('Aantal beschikbare slaapplaatsen', 'lindenhof') . '</p>';
}

function lindenhof_logo_render() {
    $value = get_option('lindenhof_logo', '');
    echo '<div class="logo-upload-field">';
    echo '<input type="hidden" name="lindenhof_logo" id="lindenhof_logo" value="' . esc_attr($value) . '" />';
    echo '<button type="button" class="button upload-logo-button" data-target="lindenhof_logo">' . esc_html__('Logo uploaden', 'lindenhof') . '</button>';
    echo '<button type="button" class="button remove-logo-button" data-target="lindenhof_logo" style="margin-left: 5px;">' . esc_html__('Verwijderen', 'lindenhof') . '</button>';
    echo '<div class="logo-preview" id="lindenhof_logo_preview">';
    if ($value) {
        $image_url = wp_get_attachment_image_url($value, 'medium');
        if ($image_url) {
            echo '<img src="' . esc_url($image_url) . '" style="max-width: 200px; max-height: 100px; margin-top: 10px; display: block;" alt="Logo preview" />';
        }
    }
    echo '</div>';
    echo '</div>';
}

function lindenhof_logo_white_render() {
    $value = get_option('lindenhof_logo_white', '');
    echo '<div class="logo-upload-field">';
    echo '<input type="hidden" name="lindenhof_logo_white" id="lindenhof_logo_white" value="' . esc_attr($value) . '" />';
    echo '<button type="button" class="button upload-logo-button" data-target="lindenhof_logo_white">' . esc_html__('Wit logo uploaden', 'lindenhof') . '</button>';
    echo '<button type="button" class="button remove-logo-button" data-target="lindenhof_logo_white" style="margin-left: 5px;">' . esc_html__('Verwijderen', 'lindenhof') . '</button>';
    echo '<div class="logo-preview" id="lindenhof_logo_white_preview">';
    if ($value) {
        $image_url = wp_get_attachment_image_url($value, 'medium');
        if ($image_url) {
            echo '<img src="' . esc_url($image_url) . '" style="max-width: 200px; max-height: 100px; margin-top: 10px; display: block; background: #333; padding: 10px;" alt="White logo preview" />';
        }
    }
    echo '</div>';
    echo '</div>';
}

function lindenhof_whatsapp_number_render() {
    $value = get_option('lindenhof_whatsapp_number', '');
    echo '<input type="tel" name="lindenhof_whatsapp_number" value="' . esc_attr($value) . '" class="regular-text" placeholder="+31612345678" />';
    echo '<p class="description">' . esc_html__('Voer het WhatsApp nummer in inclusief landcode (bijv. +31612345678).', 'lindenhof') . '</p>';
}

// Enqueue media scripts for logo upload
add_action('admin_enqueue_scripts', 'lindenhof_admin_enqueue_scripts');
function lindenhof_admin_enqueue_scripts($hook) {
    // Only load on our settings page
    if ($hook !== 'settings_page_lindenhof-settings') {
        return;
    }

    // Enqueue WordPress media scripts
    wp_enqueue_media();
}

// Settings page HTML
function lindenhof_settings_page() {
    ?>
    <div class="wrap">
        <h1><?php echo esc_html__('Lindenhof Instellingen', 'lindenhof'); ?></h1>
        
        <?php
        // Show success message if settings were saved
        if (isset($_GET['settings-updated']) && $_GET['settings-updated']) {
            echo '<div class="notice notice-success is-dismissible"><p>' . esc_html__('Instellingen opgeslagen.', 'lindenhof') . '</p></div>';
        }

        // Show success message if ACF groups were reset
        if (isset($_GET['acf_reset']) && $_GET['acf_reset'] === 'success') {
            echo '<div class="notice notice-success is-dismissible"><p>' . esc_html__('ACF Field Groups zijn herregistreerd. Ga naar Custom Fields → Field Groups om ze te bekijken.', 'lindenhof') . '</p></div>';
        }
        ?>
        
        <form action="options.php" method="post">
            <?php
            settings_fields('lindenhof_settings');
            do_settings_sections('lindenhof_settings');
            submit_button(__('Instellingen opslaan', 'lindenhof'));
            ?>
        </form>
        
        <div class="lindenhof-admin-info" style="margin-top: 40px; padding: 20px; background: #f9f9f9; border-left: 4px solid #0073aa;">
            <h3><?php echo esc_html__('Informatie', 'lindenhof'); ?></h3>
            <p><?php echo esc_html__('Deze instellingen worden gebruikt door de Lindenhof blocks en functionaliteiten.', 'lindenhof'); ?></p>
            <ul>
                <li><strong><?php echo esc_html__('Huurkalender API Key:', 'lindenhof'); ?></strong> <?php echo esc_html__('Nodig voor de boekingswidget functionaliteit.', 'lindenhof'); ?></li>
                <li><strong><?php echo esc_html__('Google API Key:', 'lindenhof'); ?></strong> <?php echo esc_html__('Nodig voor Google Reviews functionaliteit.', 'lindenhof'); ?></li>
                <li><strong><?php echo esc_html__('Accommodatie informatie:', 'lindenhof'); ?></strong> <?php echo esc_html__('Wordt getoond in de header van het intro block.', 'lindenhof'); ?></li>
                <li><strong><?php echo esc_html__('Contact informatie:', 'lindenhof'); ?></strong> <?php echo esc_html__('Wordt getoond in het contact block.', 'lindenhof'); ?></li>
                <li><strong><?php echo esc_html__('Social media:', 'lindenhof'); ?></strong> <?php echo esc_html__('Links naar social media accounts.', 'lindenhof'); ?></li>
            </ul>

            <div style="margin-top: 20px; padding: 15px; background: #fff; border: 1px solid #ddd; border-radius: 4px;">
                <h4><?php echo esc_html__('Cache beheer', 'lindenhof'); ?></h4>
                <p><?php echo esc_html__('Google Reviews worden gecached voor betere prestaties. Gebruik onderstaande knop om de cache te legen.', 'lindenhof'); ?></p>
                <button type="button" id="clear-google-reviews-cache" class="button button-secondary">
                    <?php echo esc_html__('Google Reviews cache legen', 'lindenhof'); ?>
                </button>
                <span id="cache-clear-result" style="margin-left: 10px;"></span>
            </div>

            <div style="margin-top: 20px; padding: 15px; background: #fff; border: 1px solid #ddd; border-radius: 4px;">
                <h4><?php echo esc_html__('Faciliteiten Data', 'lindenhof'); ?></h4>
                <p><?php echo esc_html__('Vul automatisch alle posts met standaard faciliteiten data zoals in het screenshot.', 'lindenhof'); ?></p>
                <button type="button" id="update-faciliteiten-data" class="button button-secondary">
                    <?php echo esc_html__('Faciliteiten Bijwerken', 'lindenhof'); ?>
                </button>
                <button type="button" id="create-faciliteiten-post" class="button button-primary" style="margin-left: 10px;">
                    <?php echo esc_html__('Nieuwe Faciliteiten Post', 'lindenhof'); ?>
                </button>
                <span id="faciliteiten-update-result" style="margin-left: 10px;"></span>
                <br><br>
                <button type="button" id="create-default-faciliteiten" class="button button-secondary">
                    <?php echo esc_html__('Maak Default Faciliteiten Posts', 'lindenhof'); ?>
                </button>
                <span id="faciliteiten-create-result" style="margin-left: 10px;"></span>
                <br><br>
                <p class="description"><?php echo esc_html__('Dit verwijdert alle bestaande faciliteiten posts en maakt nieuwe aan met de standaard structuur.', 'lindenhof'); ?></p>
                <p><a href="<?php echo admin_url('edit.php?post_type=faciliteiten'); ?>" class="button">
                    <?php echo esc_html__('Bekijk Faciliteiten Posts', 'lindenhof'); ?>
                </a></p>
            </div>

            <script>
            jQuery(document).ready(function($) {
                // Media uploader for logos
                var mediaUploader;

                $('.upload-logo-button').on('click', function(e) {
                    e.preventDefault();
                    var button = $(this);
                    var targetField = button.data('target');

                    // If the media frame already exists, reopen it
                    if (mediaUploader) {
                        mediaUploader.open();
                        return;
                    }

                    // Create the media frame
                    mediaUploader = wp.media({
                        title: '<?php echo esc_js(__('Logo selecteren', 'lindenhof')); ?>',
                        button: {
                            text: '<?php echo esc_js(__('Logo gebruiken', 'lindenhof')); ?>'
                        },
                        multiple: false,
                        library: {
                            type: 'image'
                        }
                    });

                    // When an image is selected, run a callback
                    mediaUploader.on('select', function() {
                        var attachment = mediaUploader.state().get('selection').first().toJSON();
                        $('#' + targetField).val(attachment.id);
                        $('#' + targetField + '_preview').html('<img src="' + attachment.sizes.medium.url + '" style="max-width: 200px; max-height: 100px; margin-top: 10px; display: block;" alt="Logo preview" />');
                        if (targetField === 'lindenhof_logo_white') {
                            $('#' + targetField + '_preview img').css({'background': '#333', 'padding': '10px'});
                        }
                    });

                    // Open the media frame
                    mediaUploader.open();
                });

                // Remove logo functionality
                $('.remove-logo-button').on('click', function(e) {
                    e.preventDefault();
                    var button = $(this);
                    var targetField = button.data('target');

                    $('#' + targetField).val('');
                    $('#' + targetField + '_preview').html('');
                });
                $('#clear-google-reviews-cache').on('click', function() {
                    var $button = $(this);
                    var $result = $('#cache-clear-result');

                    // Remove duplicate code - use existing AJAX structure below
                });

                $('#create-default-faciliteiten').on('click', function() {
                    var $button = $(this);
                    var $result = $('#faciliteiten-create-result');

                    if (!confirm('<?php echo esc_js(__('Dit verwijdert alle bestaande faciliteiten posts. Weet je het zeker?', 'lindenhof')); ?>')) {
                        return;
                    }

                    $button.prop('disabled', true).text('<?php echo esc_js(__('Bezig...', 'lindenhof')); ?>');
                    $result.text('');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'create_default_faciliteiten',
                            nonce: '<?php echo wp_create_nonce('create_default_faciliteiten'); ?>'
                        },
                        success: function(response) {
                            if (response.success) {
                                $result.html('<span style="color: green;">✓ ' + response.data + '</span>');
                            } else {
                                $result.html('<span style="color: red;">✗ Fout: ' + response.data + '</span>');
                            }
                        },
                        error: function() {
                            $result.html('<span style="color: red;">✗ Er is een fout opgetreden.</span>');
                        },
                        complete: function() {
                            $button.prop('disabled', false).text('<?php echo esc_js(__('Maak Default Faciliteiten Posts', 'lindenhof')); ?>');
                        }
                    });

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'clear_google_reviews_cache',
                            nonce: '<?php echo wp_create_nonce('clear_google_reviews_cache'); ?>'
                        },
                        success: function(response) {
                            if (response.success) {
                                $result.html('<span style="color: green;">✓ ' + response.data + '</span>');
                            } else {
                                $result.html('<span style="color: red;">✗ Fout: ' + response.data + '</span>');
                            }
                        },
                        error: function() {
                            $result.html('<span style="color: red;">✗ Er is een fout opgetreden.</span>');
                        },
                        complete: function() {
                            $button.prop('disabled', false).text('<?php echo esc_js(__('Google Reviews cache legen', 'lindenhof')); ?>');
                        }
                    });
                });

                // Faciliteiten update functionality
                $('#update-faciliteiten-data').on('click', function() {
                    var $button = $(this);
                    var $result = $('#faciliteiten-update-result');

                    $button.prop('disabled', true).text('<?php echo esc_js(__('Bezig met bijwerken...', 'lindenhof')); ?>');
                    $result.html('<span style="color: #0073aa;">⏳ Faciliteiten worden bijgewerkt...</span>');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'update_faciliteiten_data',
                            nonce: '<?php echo wp_create_nonce('update_faciliteiten_data'); ?>'
                        },
                        success: function(response) {
                            if (response.success) {
                                $result.html('<span style="color: green;">✅ ' + response.data.message + '</span>');
                            } else {
                                $result.html('<span style="color: red;">✗ ' + response.data.message + '</span>');
                            }
                        },
                        error: function() {
                            $result.html('<span style="color: red;">✗ Er is een fout opgetreden.</span>');
                        },
                        complete: function() {
                            $button.prop('disabled', false).text('<?php echo esc_js(__('Faciliteiten Bijwerken', 'lindenhof')); ?>');
                        }
                    });
                });

                // Create faciliteiten post functionality
                $('#create-faciliteiten-post').on('click', function() {
                    var $button = $(this);
                    var $result = $('#faciliteiten-update-result');

                    $button.prop('disabled', true).text('<?php echo esc_js(__('Bezig met aanmaken...', 'lindenhof')); ?>');
                    $result.html('<span style="color: #0073aa;">⏳ Faciliteiten post wordt aangemaakt...</span>');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'create_faciliteiten_post',
                            nonce: '<?php echo wp_create_nonce('create_faciliteiten_post'); ?>'
                        },
                        success: function(response) {
                            if (response.success) {
                                $result.html('<span style="color: green;">✅ ' + response.data.message + '</span>');
                                // Refresh page after 2 seconds
                                setTimeout(function() {
                                    window.location.href = '<?php echo admin_url('edit.php?post_type=faciliteiten'); ?>';
                                }, 2000);
                            } else {
                                $result.html('<span style="color: red;">✗ ' + response.data.message + '</span>');
                            }
                        },
                        error: function() {
                            $result.html('<span style="color: red;">✗ Er is een fout opgetreden.</span>');
                        },
                        complete: function() {
                            $button.prop('disabled', false).text('<?php echo esc_js(__('Nieuwe Faciliteiten Post', 'lindenhof')); ?>');
                        }
                    });
                });
            });
            </script>

            <h4><?php echo esc_html__('ACF Field Groups', 'lindenhof'); ?></h4>
            <p>
                <?php if (function_exists('acf_add_local_field_group')): ?>
                    <span style="color: green;">✅ ACF Pro is actief</span><br>
                    <a href="<?php echo admin_url('edit.php?post_type=acf-field-group'); ?>" class="button">
                        <?php echo esc_html__('Bekijk Field Groups', 'lindenhof'); ?>
                    </a>
                    <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=lindenhof-settings&action=reset_acf_groups'), 'reset_acf_groups'); ?>" class="button">
                        <?php echo esc_html__('Herregistreer Field Groups', 'lindenhof'); ?>
                    </a>
                <?php else: ?>
                    <span style="color: red;">❌ ACF Pro is niet actief</span><br>
                    <em><?php echo esc_html__('Installeer en activeer Advanced Custom Fields Pro om de Lindenhof blocks te gebruiken.', 'lindenhof'); ?></em>
                <?php endif; ?>
            </p>
        </div>
    </div>
    <?php
}

// Helper function to get Lindenhof options
function lindenhof_get_setting($setting_name, $default = '') {
    return get_option('lindenhof_' . $setting_name, $default);
}

// Handle ACF field groups reset
add_action('admin_init', 'lindenhof_handle_acf_reset');
function lindenhof_handle_acf_reset() {
    if (isset($_GET['action']) && $_GET['action'] === 'reset_acf_groups' &&
        isset($_GET['page']) && $_GET['page'] === 'lindenhof-settings') {

        if (!wp_verify_nonce($_GET['_wpnonce'], 'reset_acf_groups')) {
            wp_die('Security check failed');
        }

        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        // Reset and re-register field groups
        delete_option('lindenhof_acf_groups_registered');
        if (function_exists('lindenhof_register_acf_field_groups')) {
            lindenhof_register_acf_field_groups();
        }

        // Redirect with success message
        wp_redirect(add_query_arg(array(
            'page' => 'lindenhof-settings',
            'acf_reset' => 'success'
        ), admin_url('options-general.php')));
        exit;
    }
}
