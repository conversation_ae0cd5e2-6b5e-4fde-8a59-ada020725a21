<?php
/**
 * Lindenhof Theme Helper Functions
 * 
 * @package Lindenhof
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Safely output text content with proper escaping
 * 
 * @param string $text The text to output
 * @param bool $allow_html Whether to allow basic HTML tags
 */
function lindenhof_safe_text($text, $allow_html = false) {
    if (empty($text)) {
        return '';
    }
    
    if ($allow_html) {
        $allowed_tags = array(
            'p' => array(),
            'br' => array(),
            'strong' => array(),
            'em' => array(),
            'a' => array(
                'href' => array(),
                'title' => array(),
                'target' => array(),
            ),
        );
        return wp_kses($text, $allowed_tags);
    }
    
    return esc_html($text);
}

/**
 * Safely output URL with proper escaping
 *
 * @param string $url The URL to output
 * @return string Escaped URL
 */
function lindenhof_safe_url($url) {
    return esc_url($url);
}

/**
 * Get WhatsApp URL for a phone number
 *
 * @param string $number Phone number (optional, uses setting if not provided)
 * @param string $message Optional message to pre-fill
 * @return string WhatsApp URL
 */
function lindenhof_get_whatsapp_url($number = '', $message = '') {
    if (empty($number)) {
        $number = get_option('lindenhof_whatsapp_number', '');
    }

    if (empty($number)) {
        return '';
    }

    // Clean the number (remove spaces, dashes, etc.)
    $clean_number = preg_replace('/[^0-9+]/', '', $number);

    // Build WhatsApp URL
    $url = 'https://wa.me/' . ltrim($clean_number, '+');

    if (!empty($message)) {
        $url .= '?text=' . urlencode($message);
    }

    return $url;
}

/**
 * Get logo URL (normal or white version)
 *
 * @param bool $white Whether to get the white version
 * @return string Logo URL
 */
function lindenhof_get_logo_url($white = false) {
    $option_name = $white ? 'lindenhof_logo_white' : 'lindenhof_logo';
    $attachment_id = get_option($option_name, '');

    // If we have an attachment ID, get the image URL
    if (!empty($attachment_id) && is_numeric($attachment_id)) {
        $logo_url = wp_get_attachment_image_url($attachment_id, 'full');
        if ($logo_url) {
            return $logo_url;
        }
    }

    // Fallback to default logo if no custom logo is set
    $default_logo = $white ? 'logo-white.png' : 'logo.png';
    $logo_url = get_template_directory_uri() . '/' . $default_logo;

    return $logo_url;
}

/**
 * Safely output image with proper attributes and escaping
 * 
 * @param array $image ACF image array
 * @param string $size Image size
 * @param string $class CSS classes
 * @param string $alt Alt text override
 * @return string Image HTML
 */
function lindenhof_safe_image($image, $size = 'large', $class = '', $alt = '') {
    if (empty($image) || !is_array($image)) {
        return '';
    }
    
    $src = isset($image['sizes'][$size]) ? $image['sizes'][$size] : $image['url'];
    $alt_text = !empty($alt) ? $alt : ($image['alt'] ?? '');
    $title = $image['title'] ?? '';
    
    $attributes = array(
        'src' => esc_url($src),
        'alt' => esc_attr($alt_text),
        'loading' => 'lazy'
    );
    
    if (!empty($title)) {
        $attributes['title'] = esc_attr($title);
    }
    
    if (!empty($class)) {
        $attributes['class'] = esc_attr($class);
    }
    
    $attr_string = '';
    foreach ($attributes as $key => $value) {
        $attr_string .= sprintf(' %s="%s"', $key, $value);
    }
    
    return sprintf('<img%s>', $attr_string);
}

/**
 * Safely output ACF link field
 * 
 * @param array $link ACF link array
 * @param string $class CSS classes
 * @param string $content Link content override
 * @return string Link HTML
 */
function lindenhof_safe_link($link, $class = '', $content = '') {
    if (empty($link) || !is_array($link) || empty($link['url'])) {
        return '';
    }
    
    $url = esc_url($link['url']);
    $title = esc_attr($link['title'] ?? '');
    $target = !empty($link['target']) ? esc_attr($link['target']) : '_self';
    $link_content = !empty($content) ? $content : esc_html($title);
    
    $attributes = array(
        'href' => $url,
        'title' => $title,
        'target' => $target
    );
    
    if (!empty($class)) {
        $attributes['class'] = esc_attr($class);
    }
    
    $attr_string = '';
    foreach ($attributes as $key => $value) {
        $attr_string .= sprintf(' %s="%s"', $key, $value);
    }
    
    return sprintf('<a%s>%s</a>', $attr_string, $link_content);
}

/**
 * Generate BEM-style CSS classes
 * 
 * @param string $block Block name
 * @param string $element Element name (optional)
 * @param string $modifier Modifier name (optional)
 * @param array $additional Additional classes
 * @return string CSS classes
 */
function lindenhof_bem_classes($block, $element = '', $modifier = '', $additional = array()) {
    $classes = array();
    
    // Block
    $base_class = 'lindenhof-' . $block;
    $classes[] = $base_class;
    
    // Element
    if (!empty($element)) {
        $classes[] = $base_class . '__' . $element;
    }
    
    // Modifier
    if (!empty($modifier)) {
        $base = !empty($element) ? $base_class . '__' . $element : $base_class;
        $classes[] = $base . '--' . $modifier;
    }
    
    // Additional classes
    if (!empty($additional) && is_array($additional)) {
        $classes = array_merge($classes, $additional);
    }
    
    return esc_attr(implode(' ', array_filter($classes)));
}

/**
 * Get responsive image sizes attribute
 * 
 * @param string $breakpoint_sizes Sizes for different breakpoints
 * @return string Sizes attribute
 */
function lindenhof_responsive_sizes($breakpoint_sizes = '(max-width: 768px) 100vw, 50vw') {
    return esc_attr($breakpoint_sizes);
}

/**
 * Format date for display
 * 
 * @param string $date Date string
 * @param string $format Date format
 * @return string Formatted date
 */
function lindenhof_format_date($date, $format = 'j F Y') {
    if (empty($date)) {
        return '';
    }
    
    $timestamp = is_numeric($date) ? $date : strtotime($date);
    return date_i18n($format, $timestamp);
}

/**
 * Truncate text to specified length
 * 
 * @param string $text Text to truncate
 * @param int $length Maximum length
 * @param string $suffix Suffix to add if truncated
 * @return string Truncated text
 */
function lindenhof_truncate_text($text, $length = 150, $suffix = '...') {
    if (empty($text) || strlen($text) <= $length) {
        return $text;
    }
    
    return substr($text, 0, $length) . $suffix;
}

/**
 * Check if we're on the front page
 * 
 * @return bool
 */
function lindenhof_is_front_page() {
    return is_front_page() || is_home();
}

/**
 * Get theme option with fallback
 * 
 * @param string $option_name Option name
 * @param mixed $default Default value
 * @return mixed Option value
 */
function lindenhof_get_option($option_name, $default = '') {
    $value = get_option($option_name, $default);
    return !empty($value) ? $value : $default;
}

/**
 * Generate unique ID for elements
 * 
 * @param string $prefix Prefix for the ID
 * @return string Unique ID
 */
function lindenhof_unique_id($prefix = 'lindenhof') {
    static $counter = 0;
    $counter++;
    return esc_attr($prefix . '-' . $counter . '-' . wp_rand(1000, 9999));
}

/**
 * Add data attributes for Swup navigation
 * 
 * @param array $attributes Additional attributes
 * @return string Data attributes
 */
function lindenhof_swup_attributes($attributes = array()) {
    $default_attributes = array(
        'data-swup' => '0'
    );
    
    $merged_attributes = array_merge($default_attributes, $attributes);
    
    $attr_string = '';
    foreach ($merged_attributes as $key => $value) {
        $attr_string .= sprintf(' %s="%s"', esc_attr($key), esc_attr($value));
    }
    
    return $attr_string;
}

/**
 * Get faciliteiten posts
 *
 * @param int $limit Number of posts to retrieve
 * @return array Array of post objects
 */
function lindenhof_get_faciliteiten($limit = -1) {
    return get_posts(array(
        'post_type' => 'faciliteiten',
        'posts_per_page' => $limit,
        'post_status' => 'publish',
        'meta_key' => 'volgorde',
        'orderby' => 'meta_value_num',
        'order' => 'ASC',
    ));
}

/**
 * Get restaurants posts
 *
 * @param int $limit Number of posts to retrieve
 * @return array Array of post objects
 */
function lindenhof_get_restaurants($limit = -1) {
    return get_posts(array(
        'post_type' => 'eten_drinken',
        'posts_per_page' => $limit,
        'post_status' => 'publish',
        'orderby' => 'title',
        'order' => 'ASC',
    ));
}

/**
 * Get activiteiten posts
 *
 * @param int $limit Number of posts to retrieve
 * @param string $categorie Filter by category
 * @return array Array of post objects
 */
function lindenhof_get_activiteiten($limit = -1, $categorie = '') {
    $args = array(
        'post_type' => 'activiteiten',
        'posts_per_page' => $limit,
        'post_status' => 'publish',
        'orderby' => 'title',
        'order' => 'ASC',
    );

    if ($categorie) {
        $args['meta_query'] = array(
            array(
                'key' => 'categorie',
                'value' => $categorie,
                'compare' => '=',
            ),
        );
    }

    return get_posts($args);
}

/**
 * Get galerij photos
 *
 * @param int $limit Number of posts to retrieve
 * @param string $categorie Filter by category
 * @return array Array of post objects
 */
function lindenhof_get_galerij($limit = -1, $categorie = '') {
    $args = array(
        'post_type' => 'galerij',
        'posts_per_page' => $limit,
        'post_status' => 'publish',
        'meta_key' => 'volgorde',
        'orderby' => 'meta_value_num',
        'order' => 'ASC',
    );

    if ($categorie) {
        $args['meta_query'] = array(
            array(
                'key' => 'categorie',
                'value' => $categorie,
                'compare' => '=',
            ),
        );
    }

    return get_posts($args);
}
