/**
 * Booking Form Handler
 * Handles the sticky booking form and scrolls to huurkal<PERSON> with date parameters
 */

$(document).ready(function() {
    $(document).on("initPage", function() {
        initBookingForm();
        setupIframeMessageListener();
    });
});

function initBookingForm() {
    // Handle sticky booking form submission
    $('#sticky-date-form').off('submit.bookingForm').on('submit.bookingForm', function(e) {
        e.preventDefault();
        
        const arrivalDate = $('#arrival-date').val();
        const departureDate = $('#departure-date').val();
        
        // Validate dates
        if (!arrivalDate || !departureDate) {
            alert('Vul beide datums in.');
            return;
        }
        
        if (new Date(arrivalDate) >= new Date(departureDate)) {
            alert('De vertrekdatum moet na de aankomstdatum zijn.');
            return;
        }
        
        // Close sticky booking widget if it's open
        $('.intro-block__booking-close').trigger('click');
        
        // Scroll to huurkalender block
        scrollToHuurkalender(arrivalDate, departureDate);
    });
    
    // Auto-update departure date when arrival date changes
    $('#arrival-date').off('change.bookingForm').on('change.bookingForm', function() {
        const arrivalDate = new Date($(this).val());
        const departureInput = $('#departure-date');
        
        if (arrivalDate) {
            // Set minimum departure date to day after arrival
            const minDeparture = new Date(arrivalDate);
            minDeparture.setDate(minDeparture.getDate() + 1);
            
            const minDepartureStr = minDeparture.toISOString().split('T')[0];
            departureInput.attr('min', minDepartureStr);
            
            // If current departure date is before new minimum, update it
            const currentDeparture = new Date(departureInput.val());
            if (!departureInput.val() || currentDeparture <= arrivalDate) {
                departureInput.val(minDepartureStr);
            }
        }
    });
}

function scrollToHuurkalender(arrivalDate, departureDate) {
    // Find the huurkalender block
    const huurkalenderBlock = $('[data-block-type="huurkalender"]');
    
    if (huurkalenderBlock.length === 0) {
        console.warn('Huurkalender block not found on this page');
        return;
    }
    
    // Calculate scroll position (accounting for header)
    const headerHeight = $('header').outerHeight() || 0;
    const targetPosition = huurkalenderBlock.offset().top - headerHeight - 20;
    
    // Smooth scroll to huurkalender
    if (typeof scroller !== 'undefined' && scroller.scrollTo) {
        // Use Lenis scroller if available
        scroller.scrollTo(targetPosition, {
            duration: 1.2,
            easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
            onComplete: () => {
                // Pass dates to huurkalender after scroll
                passDateToHuurkalender(arrivalDate, departureDate);
            }
        });
    } else {
        // Fallback to jQuery animate
        $('html, body').animate({
            scrollTop: targetPosition
        }, 1200, function() {
            // Pass dates to huurkalender after scroll
            passDateToHuurkalender(arrivalDate, departureDate);
        });
    }
}

function passDateToHuurkalender(arrivalDate, departureDate) {
    // Show the booking section
    const bookingSection = $('#huurkalender-booking-section');
    if (bookingSection.length > 0) {
        bookingSection.show();
    }

    // Find the huurkalender booking iframe
    const huurkalenderIframe = $('[data-block-type="huurkalender"] iframe[id*="iframe_huurkalender_booking"]');

    if (huurkalenderIframe.length === 0) {
        console.warn('Huurkalender booking iframe not found');
        return;
    }

    try {
        // Calculate nights
        const arrival = new Date(arrivalDate);
        const departure = new Date(departureDate);
        const nights = Math.ceil((departure - arrival) / (1000 * 60 * 60 * 24));

        // Format dates for huurkalender (DD-MM-YYYY)
        const formatDate = (date) => {
            const d = new Date(date);
            const day = String(d.getDate()).padStart(2, '0');
            const month = String(d.getMonth() + 1).padStart(2, '0');
            const year = d.getFullYear();
            return `${day}-${month}-${year}`;
        };

        const formattedArrival = formatDate(arrivalDate);

        // Get current iframe src and add parameters
        const currentSrc = huurkalenderIframe.attr('src');
        const url = new URL(currentSrc);

        // Add date parameters (multiple formats to increase compatibility)
        url.searchParams.set('arrival_date', formattedArrival);
        url.searchParams.set('nights', nights);
        url.searchParams.set('aankomst', formattedArrival);
        url.searchParams.set('nachten', nights);
        url.searchParams.set('auto_fill', '1');
        url.searchParams.set('auto_submit', '1');

        // Update iframe src with new parameters
        huurkalenderIframe.attr('src', url.toString());

        console.log('Dates passed to huurkalender:', {
            arrival: formattedArrival,
            nights: nights,
            url: url.toString()
        });

        // Wait for iframe to load, then try to communicate with it
        huurkalenderIframe.on('load', function() {
            setTimeout(() => {
                // Send postMessage to iframe (this is the safest cross-origin method)
                sendMessageToHuurkalender(huurkalenderIframe[0], formattedArrival, nights);
            }, 1000);
        });

        // Scroll to booking section after a short delay
        setTimeout(() => {
            if (bookingSection.length > 0) {
                const headerHeight = $('header').outerHeight() || 0;
                const targetPosition = bookingSection.offset().top - headerHeight - 20;

                if (typeof scroller !== 'undefined' && scroller.scrollTo) {
                    scroller.scrollTo(targetPosition, { duration: 0.8 });
                } else {
                    $('html, body').animate({ scrollTop: targetPosition }, 800);
                }
            }
        }, 500);

    } catch (error) {
        console.error('Error passing dates to huurkalender:', error);
    }
}

function sendMessageToHuurkalender(iframe, arrivalDate, nights) {
    try {
        // Send postMessage to iframe with booking data
        iframe.contentWindow.postMessage({
            type: 'LINDENHOF_BOOKING_DATA',
            action: 'FILL_AND_SUBMIT',
            data: {
                arrivalDate: arrivalDate,
                nights: nights,
                autoSubmit: true,
                source: 'lindenhof-sticky-form'
            }
        }, '*');

        console.log('Sent booking data to huurkalender iframe:', {
            arrivalDate: arrivalDate,
            nights: nights
        });

        // Also try alternative message formats that huurkalender might recognize
        setTimeout(() => {
            iframe.contentWindow.postMessage({
                type: 'BOOKING_PREFILL',
                aankomst: arrivalDate,
                nachten: nights,
                auto_submit: true
            }, '*');
        }, 500);

    } catch (error) {
        console.log('Could not send message to iframe:', error);
    }
}

function setupIframeMessageListener() {
    // Listen for messages from the huurkalender iframe
    window.addEventListener('message', function(event) {
        // Only process messages from huurkalender.nl
        if (event.origin !== 'https://www.huurkalender.nl') {
            return;
        }

        console.log('Received message from huurkalender iframe:', event.data);

        // Handle different types of messages
        if (event.data.type === 'FORM_FILLED') {
            console.log('Huurkalender form was successfully filled');
        } else if (event.data.type === 'FORM_SUBMITTED') {
            console.log('Huurkalender form was successfully submitted');
        } else if (event.data.type === 'BOOKING_COMPLETE') {
            console.log('Booking process completed');
        }
    });
}
