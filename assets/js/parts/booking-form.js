/**
 * Booking Form Handler
 * Handles the sticky booking form and scrolls to huurkalender with date parameters
 */

$(document).ready(function() {
    $(document).on("initPage", function() {
        initBookingForm();
    });
});

function initBookingForm() {
    // Handle sticky booking form submission
    $('#sticky-date-form').off('submit.bookingForm').on('submit.bookingForm', function(e) {
        e.preventDefault();
        
        const arrivalDate = $('#arrival-date').val();
        const departureDate = $('#departure-date').val();
        
        // Validate dates
        if (!arrivalDate || !departureDate) {
            alert('Vul beide datums in.');
            return;
        }
        
        if (new Date(arrivalDate) >= new Date(departureDate)) {
            alert('De vertrekdatum moet na de aankomstdatum zijn.');
            return;
        }
        
        // Close sticky booking widget if it's open
        $('.intro-block__booking-close').trigger('click');
        
        // Scroll to huurkalender block
        scrollToHuurkalender(arrivalDate, departureDate);
    });
    
    // Auto-update departure date when arrival date changes
    $('#arrival-date').off('change.bookingForm').on('change.bookingForm', function() {
        const arrivalDate = new Date($(this).val());
        const departureInput = $('#departure-date');
        
        if (arrivalDate) {
            // Set minimum departure date to day after arrival
            const minDeparture = new Date(arrivalDate);
            minDeparture.setDate(minDeparture.getDate() + 1);
            
            const minDepartureStr = minDeparture.toISOString().split('T')[0];
            departureInput.attr('min', minDepartureStr);
            
            // If current departure date is before new minimum, update it
            const currentDeparture = new Date(departureInput.val());
            if (!departureInput.val() || currentDeparture <= arrivalDate) {
                departureInput.val(minDepartureStr);
            }
        }
    });
}

function scrollToHuurkalender(arrivalDate, departureDate) {
    // Find the huurkalender block
    const huurkalenderBlock = $('[data-block-type="huurkalender"]');
    
    if (huurkalenderBlock.length === 0) {
        console.warn('Huurkalender block not found on this page');
        return;
    }
    
    // Calculate scroll position (accounting for header)
    const headerHeight = $('header').outerHeight() || 0;
    const targetPosition = huurkalenderBlock.offset().top - headerHeight - 20;
    
    // Smooth scroll to huurkalender
    if (typeof scroller !== 'undefined' && scroller.scrollTo) {
        // Use Lenis scroller if available
        scroller.scrollTo(targetPosition, {
            duration: 1.2,
            easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
            onComplete: () => {
                // Pass dates to huurkalender after scroll
                passDateToHuurkalender(arrivalDate, departureDate);
            }
        });
    } else {
        // Fallback to jQuery animate
        $('html, body').animate({
            scrollTop: targetPosition
        }, 1200, function() {
            // Pass dates to huurkalender after scroll
            passDateToHuurkalender(arrivalDate, departureDate);
        });
    }
}

function passDateToHuurkalender(arrivalDate, departureDate) {
    // Show the booking section
    const bookingSection = $('#huurkalender-booking-section');
    if (bookingSection.length > 0) {
        bookingSection.show();
    }

    // Find the huurkalender booking iframe
    const huurkalenderIframe = $('[data-block-type="huurkalender"] iframe[id*="iframe_huurkalender_booking"]');

    if (huurkalenderIframe.length === 0) {
        console.warn('Huurkalender booking iframe not found');
        return;
    }

    try {
        // Calculate nights
        const arrival = new Date(arrivalDate);
        const departure = new Date(departureDate);
        const nights = Math.ceil((departure - arrival) / (1000 * 60 * 60 * 24));

        // Format dates for huurkalender (DD-MM-YYYY)
        const formatDate = (date) => {
            const d = new Date(date);
            const day = String(d.getDate()).padStart(2, '0');
            const month = String(d.getMonth() + 1).padStart(2, '0');
            const year = d.getFullYear();
            return `${day}-${month}-${year}`;
        };

        const formattedArrival = formatDate(arrivalDate);

        // Get current iframe src and add parameters
        const currentSrc = huurkalenderIframe.attr('src');
        const url = new URL(currentSrc);

        // Add date parameters (multiple formats to increase compatibility)
        url.searchParams.set('arrival_date', formattedArrival);
        url.searchParams.set('nights', nights);
        url.searchParams.set('aankomst', formattedArrival);
        url.searchParams.set('nachten', nights);
        url.searchParams.set('auto_fill', '1');
        url.searchParams.set('auto_submit', '1');

        // Update iframe src with new parameters
        huurkalenderIframe.attr('src', url.toString());

        console.log('Dates passed to huurkalender:', {
            arrival: formattedArrival,
            nights: nights,
            url: url.toString()
        });

        // Wait for iframe to load, then try to auto-fill and submit
        huurkalenderIframe.on('load', function() {
            setTimeout(() => {
                try {
                    // Try to auto-fill the form in the iframe
                    autoFillHuurkalenderForm(huurkalenderIframe[0], formattedArrival, nights);
                } catch (error) {
                    console.log('Could not auto-fill iframe form (cross-origin restriction):', error);
                }
            }, 1000);
        });

        // Scroll to booking section after a short delay
        setTimeout(() => {
            if (bookingSection.length > 0) {
                const headerHeight = $('header').outerHeight() || 0;
                const targetPosition = bookingSection.offset().top - headerHeight - 20;

                if (typeof scroller !== 'undefined' && scroller.scrollTo) {
                    scroller.scrollTo(targetPosition, { duration: 0.8 });
                } else {
                    $('html, body').animate({ scrollTop: targetPosition }, 800);
                }
            }
        }, 500);

    } catch (error) {
        console.error('Error passing dates to huurkalender:', error);
    }
}

function autoFillHuurkalenderForm(iframe, arrivalDate, nights) {
    try {
        // Try to access iframe content (may fail due to cross-origin restrictions)
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

        if (!iframeDoc) {
            console.log('Cannot access iframe content due to cross-origin restrictions');
            return;
        }

        // Look for common form elements in huurkalender
        const arrivalInput = iframeDoc.querySelector('input[name*="arrival"], input[name*="aankomst"], input[name*="van"], input[type="date"]');
        const nightsInput = iframeDoc.querySelector('input[name*="nights"], input[name*="nachten"], input[name*="aantal"]');
        const submitButton = iframeDoc.querySelector('button[type="submit"], input[type="submit"], .submit-btn, .book-btn, .reserveer');

        // Fill arrival date
        if (arrivalInput) {
            // Convert DD-MM-YYYY to YYYY-MM-DD for date inputs
            const dateParts = arrivalDate.split('-');
            const isoDate = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;

            arrivalInput.value = isoDate;
            arrivalInput.dispatchEvent(new Event('change', { bubbles: true }));
            console.log('Filled arrival date:', isoDate);
        }

        // Fill nights
        if (nightsInput) {
            nightsInput.value = nights;
            nightsInput.dispatchEvent(new Event('change', { bubbles: true }));
            console.log('Filled nights:', nights);
        }

        // Auto-submit after a short delay
        if (submitButton) {
            setTimeout(() => {
                submitButton.click();
                console.log('Auto-clicked submit button');
            }, 500);
        }

    } catch (error) {
        console.log('Could not auto-fill form due to cross-origin restrictions:', error);

        // Alternative approach: try to send a postMessage to the iframe
        try {
            iframe.contentWindow.postMessage({
                type: 'FILL_BOOKING_FORM',
                data: {
                    arrivalDate: arrivalDate,
                    nights: nights,
                    autoSubmit: true
                }
            }, '*');
            console.log('Sent postMessage to iframe');
        } catch (postError) {
            console.log('PostMessage also failed:', postError);
        }
    }
}
