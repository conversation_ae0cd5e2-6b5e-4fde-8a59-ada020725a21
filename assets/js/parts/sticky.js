$(document).ready(function(){

  $(document).on("initPage", function(){
    initializeHomeAboutBlock();
  });
});

function initializeHomeAboutBlock() {
  if ($(".introBlock").length > 0) {
    setTimeout(function() {
      setHomeAboutBlock();
    }, 300);
  }
}

function setHomeAboutBlock() {
  var $textWrapper = $('.stickyBooking');
  var $imageWrapper = $('.introBlock .col:last-child');

  var originalOffsetTop = $textWrapper.offset().top;
  var wrapperHeight = $imageWrapper.outerHeight();
  var textWrapperHeight = $textWrapper.outerHeight();

  scroller.on('scroll', function() {
    var scrollTop = $(window).scrollTop();
    var stickyStart = originalOffsetTop - $('header').outerHeight() * 2;
    var stickyEnd = stickyStart + wrapperHeight - textWrapperHeight - 20;

    if (scrollTop >= stickyStart && scrollTop <= stickyEnd) {
      $textWrapper.css({
        top: (scrollTop - stickyStart) + 'px'
      });
    } else if (scrollTop > stickyEnd) {
      $textWrapper.css({
        top: (wrapperHeight - textWrapperHeight) + 'px'
      });
    } else {
      $textWrapper.css({
        top: '0px'
      });
    }
  });
}
