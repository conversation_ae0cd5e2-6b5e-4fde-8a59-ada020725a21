// out: false

@font-face {
  font-family: 'icomoon';
  src:  url('assets/fonts/icomoon.eot?twq3hi');
  src:  url('assets/fonts/icomoon.eot?twq3hi#iefix') format('embedded-opentype'),
    url('assets/fonts/icomoon.ttf?twq3hi') format('truetype'),
    url('assets/fonts/icomoon.woff?twq3hi') format('woff'),
    url('assets/fonts/icomoon.svg?twq3hi#icomoon') format('svg');
  font-weight: normal;
  font-style: normal; 
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-balloon:before {
  content: "\e900";
}
.icon-bed:before {
  content: "\e901";
}
.icon-calendar:before {
  content: "\e902";
}
.icon-checkmark:before {
  content: "\e903";
}
.icon-chevron-down:before {
  content: "\e904";
}
.icon-chevron-left:before {
  content: "\e905";
}
.icon-chevron-right:before {
  content: "\e906";
}
.icon-chevron-up:before {
  content: "\e907";
}
.icon-close:before {
  content: "\e908";
}
.icon-facebook:before {
  content: "\e909";
}
.icon-instagram:before {
  content: "\e90a";
}
.icon-lindenhof:before {
  content: "\e90b";
}
.icon-location:before {
  content: "\e90c";
}
.icon-mail:before {
  content: "\e90d";
}
.icon-person:before {
  content: "\e90e";
}
.icon-phone:before {
  content: "\e90f";
}
.icon-star_half:before {
  content: "\e910";
}
.icon-star:before {
  content: "\e911";
}
.icon-whatsapp:before {
  content: "\e912";
}
.icon-x:before {
  content: "\e913";
}

.hugeTitle, .biggerTitle, .bigTitle, .subTitle, .text, .writtenTitle, .title, .textTitle {
  &.white {
    color: @hardWhite;
  }
  &.secondary {
    color: @secondaryColor;
  }
}
.hugeTitle {
  font-size: @vw100 + @vw40; 
  font-family: "Playfair Display", Arial;
  font-weight: 400;
}

section {
  &.inview {
    [data-letters] {
      visibility: visible;
      .letter {
        .transform(translateY(0));
        opacity: 1;
        transition: opacity 0.6s cubic-bezier(0.76, 0, 0.24, 1), transform 0.6s cubic-bezier(0.76, 0, 0.24, 1);
        -webkit-transition: opacity 0.6s cubic-bezier(0.76, 0, 0.24, 1), transform 0.6s cubic-bezier(0.76, 0, 0.24, 1);
        .stagger(30, 0.015s);
      }
    }
  }
}

[data-letters] {
  visibility: hidden;
  .letter {
    .transform(translateY(@vw10));
    opacity: 0;
  }
}

.title {
  font-size: @vw36;
  font-family: "Playfair Display", Arial;
  font-style: italic;
  font-weight: 300;
  &:not(.bold) {
    &:before {
      content: "\e90b";
      font-family: 'Icomoon';
      font-size: @vw24;
      line-height: 1;
      display: inline-block;
      position: relative;
      top: -0.1em;
      margin-right: @vw10;
    }
  }
  &.bold {
    font-weight: 700;
  }
}

.textTitle {
  font-size: @vw18;
  line-height: 1.4;
  font-family: "Inter", Arial;
  font-weight: 600;
}

.text {
  p {
    line-height: 1.4;
    font-size: @vw18;
    &:not(:last-child) {
      margin-bottom: @vw22;
    }
  }
}

@media all and (max-width: 1160px) {
  .text {
    &:not(:first-child) {
      margin-top: @vw20-1160;
    }

    p {
      &:not(:last-child) {
        margin-bottom: @vw22-1160;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .text {
    
    &:not(:first-child) {
      margin-top: @vw20-580;
    }

    p {
      &:not(:last-child) {
        margin-bottom: @vw22-580;
      }
    }
  }
}
