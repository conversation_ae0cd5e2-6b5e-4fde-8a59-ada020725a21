<?php
/**
 * Intro Block Template
 * 
 * @package Lindenhof
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get ACF fields
$title = get_field('title');
$subtitle = get_field('subtitle');
$content = get_field('content');
$background_image = get_field('background_image');
$gallery = get_field('gallery');

// Google Reviews fields
$show_google_reviews = get_field('show_google_reviews');
$google_place_id = get_field('google_place_id');

// Content sections fields
$show_faciliteiten = get_field('show_faciliteiten');
$show_eten_drinken = get_field('show_eten_drinken');
$show_omgeving = get_field('show_omgeving');
$show_algemeen = get_field('show_algemeen');

// Booking widget fields
$show_sticky_booking = get_field('show_sticky_booking');
$booking_widget_type = get_field('booking_widget_type');
$hotelhuurder_embed = get_field('hotelhuurder_embed');

// Get accommodatie info from settings
$accommodatie_locatie = lindenhof_get_setting('accommodatie_locatie');
$accommodatie_personen = lindenhof_get_setting('accommodatie_personen');
$accommodatie_slaapplaatsen = lindenhof_get_setting('accommodatie_slaapplaatsen');

// Get Google Reviews data if needed
$reviews_data = null;
if ($show_google_reviews && $google_place_id) {
    $reviews_data = lindenhof_get_google_reviews($google_place_id);
    // Check if it's a WP_Error and set to null if so
    if (is_wp_error($reviews_data)) {
        $reviews_data = null;
    }
}

// Generate unique ID for this block instance
$block_id = $block_id ?? lindenhof_unique_id('introBlock');

// Build CSS classes
$css_classes = lindenhof_bem_classes('introBlock', '', '', array('lindenhof-block'));

// Build inline styles for background image
$inline_styles = '';
if ($background_image && !empty($background_image['url'])) {
    $inline_styles = sprintf(
        'style="background-image: url(%s);"',
        esc_url($background_image['url'])
    );
}
?>

<section class="introBlock" data-anchor="home" data-init>
    <div class="contentWrapper">
        <?php if ($gallery && is_array($gallery)): ?>
            <div class="backgroundGallery">
                 <?php $gallery = array_slice($gallery, 0, 3); ?>
                  <?php $gallery = array_reverse($gallery); ?>
                <?php foreach ($gallery as $image): ?>
                     <?php if ($image == $gallery[0]): ?>
                        <div class="innerCol">
                            <div class="galleryItem" data-gallery data-image="<?php echo esc_url($image['url']); ?>">
                                <?php echo lindenhof_safe_image($image, 'large', lindenhof_bem_classes('intro-block', 'gallery-image')); ?>
                            </div>
                        </div>
                        <div class="innerCol">
                    <?php else: ?>
                            <div class="galleryItem" data-gallery data-image="<?php echo esc_url($image['url']); ?>">
                                <?php echo lindenhof_safe_image($image, 'medium_large', lindenhof_bem_classes('intro-block', 'gallery-image')); ?>
                            </div>
                    <?php endif; ?>
                    
                <?php endforeach; ?>
            </div>
            <div class="arrows">
                <div class="arrow prev">
                    <i class="icon-chevron-left"></i>
                </div>
                <div class="arrow next">
                    <i class="icon-chevron-right"></i>
                </div>
            </div>
        </div>
        <?php endif; ?>
        <div class="cols">
        <div class="col">
            <div class="innerCol">
                <div class="logo">
                     <img src="<?php echo esc_url(lindenhof_get_logo_url()); ?>" alt="Logo | <?php echo get_bloginfo('name'); ?>">
                </div>
            </div>
        <?php if ($title): ?>
            <div class="innerCol">
                <div class="headerWrapper">
                    <div class="headerInfo">
                        <?php if ($show_google_reviews && $google_place_id && $reviews_data && isset($reviews_data['rating'])): ?>
                            <div class="googleReviews">
                                <?php echo lindenhof_render_google_stars($reviews_data['rating']); ?>
                                <span class="rating">
                                    <?php echo esc_html($reviews_data['rating']); ?>
                                </span>
                                <span class="totalRatings">
                                    (<?php echo esc_html($reviews_data['total_ratings'] ?? 0); ?>)
                                </span>
                            </div>
                        <?php endif; ?>
                            <div class="specs">
                        <?php if ($accommodatie_locatie): ?>
                            <div class="spec">
                                <i class="icon-location"></i>
                                <span>
                                    <?php echo esc_html($accommodatie_locatie); ?>
                                </span>
                            </div>
                        <?php endif; ?>
                            |
                        <?php if ($accommodatie_personen): ?>
                            <div class="spec">
                                <i class="icon-person"></i>
                                <span>
                                    <?php echo sprintf(__('%d', 'lindenhof'), $accommodatie_personen); ?>
                                </span>
                            </div>
                        <?php endif; ?>
                            |
                        <?php if ($accommodatie_slaapplaatsen): ?>
                            <div class="spec">
                                <i class="icon-bed"></i>
                                <span>
                                    <?php echo sprintf(__('%d', 'lindenhof'), $accommodatie_slaapplaatsen); ?>
                                </span>
                            </div>
                        <?php endif; ?>
                        </div>
                    </div>

                    <h1 class="title bold" data-lines data-letters>
                        <?php echo lindenhof_safe_text($title); ?>
                    </h1>

                    <div class="text">
                        <?php echo html_entity_decode($content); ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="divider"></div>
         <div class="innerCol">
            <!-- algemeen -->
             <?php if ($show_algemeen): ?>
                <div class="algemeen">
                    <?php echo get_template_part('template-parts/blocks/algemeen-block'); ?>
                </div>
            <?php endif; ?>
        </div>
        <div class="divider"></div>
        <!-- faciliteiten -->
            <?php if ($show_faciliteiten): ?>
            <div class="faciliteiten">
                <?php echo get_template_part('template-parts/blocks/faciliteiten-block'); ?>
            </div>
        <?php endif; ?>
        <div class="divider"></div>
        <div class="innerCol">
             <?php if ($show_omgeving): ?>
                <div class="omgeving">
                    <?php echo get_template_part('template-parts/blocks/omgeving-block'); ?>
                </div>
            <?php endif; ?>
        </div>
        <div class="divider"></div>
    </div>
    <div class="col">
    <?php if ($show_sticky_booking): ?>
        <div class="stickyBooking" data-widget-type="<?php echo esc_attr($booking_widget_type); ?>" data-sticky-top>
            <img src="<?php echo get_template_directory_uri(); ?>/assets/img/raaf.gif" alt="Raaf" class="crow">
            <svg viewBox="0 0 1985.95 290.21">
                <path d="M841.41,169.4c4.12-5.31,7.74-9.95,11.72-15.08,5.5,2.9,11.34,5.93,17.13,9.06,2.17,1.17,4.08,2.91,6.35,3.77,3.6,1.38,3.11,3.74,1.44,5.82-4.15,5.19-8.36,10.38-13.08,15.04-4.06,4.01-8.67,7.54-13.35,10.83-5.67,3.99-19.5,2.8-25.53-1.81-2.96-2.26-5.65-4.98-8.85-6.78-4.87-2.73-5.75-7.8-8.2-12-4.77-8.16-5.55-17.1-5.2-26.14.39-10.22.61-20.59,2.51-30.58,1.51-7.94,4.48-15.97,8.53-22.96,5.76-9.97,12.43-19.61,19.93-28.35,9.81-11.44,22.2-20.06,36.4-25.28,9.14-3.36,19.08-4.57,28.22-7.93,14.12-5.19,28.47-2.18,42.7-2.34,5.33-.06,10.69,2.23,16.02,3.52,2.67.65,5.42,1.21,7.94,2.27,7.76,3.25,15.66,6.3,23.04,10.27,4.24,2.28,7.68,6.11,11.32,9.42,3.57,3.25,7.4,6.38,10.27,10.2,3.58,4.77,6.15,10.29,9.42,15.32,9.83,15.13,7.15,32.02,6.33,48.4-.57,11.48-5.23,22.59-11.26,32.02-7.13,11.17-15.72,21.51-27.11,29.46-9.39,6.55-18.99,12.41-29.5,16.63-9.04,3.63-18.63,5.87-27.71,9.42-14.96,5.85-30.86,6.27-46.36,9.11-45.86,8.43-92.01,3.78-138.04,4.15-10.43.08-21.07-.15-31.27-2.04-16.98-3.15-34.11-3.56-51.2-4.92-13.02-1.04-26.02-2.3-38.99-3.82-9.73-1.14-19.39-2.86-29.08-4.31-.99-.15-1.99-.18-2.98-.26-10.58-.82-21.16-1.56-31.73-2.49-5.42-.48-10.86-1.01-16.19-2.01-10.37-1.95-20.66-4.35-31.02-6.39-9.89-1.95-19.82-3.67-29.73-5.49-7.27-1.34-14.91-1.7-21.71-4.27-9.57-3.6-19.86-3.75-29.23-7.04-12.58-4.41-25.67-5.67-38.44-8.71-7.18-1.71-14.19-4.07-21.36-5.84-8.82-2.18-17.7-4.17-26.59-6.03-7.7-1.61-15.78-2.01-23.14-4.56-10.8-3.73-21.92-4.87-33.02-6.68-5.53-.9-10.91-2.78-16.36-4.21-.48-.13-.97-.23-1.47-.28-7.39-.79-14.78-1.58-22.18-2.36-6.73-.71-13.6-.8-20.18-2.21-31.3-6.69-62.98-4.6-94.56-4.33-6.26.05-12.59.81-18.74,1.99-10.22,1.96-20.61,3.69-30.42,7.03-12.65,4.3-25.69,8.31-36.73,16.13-5.41,3.84-9.79,8.97-13.52,14.94-5.98,9.57-5.25,19.37-2.17,28.7,2.89,8.76,7.74,17.13,13.03,24.77,3.73,5.37,9,9.7,14.79,13.59,6.92,4.65,14.63,5.71,21.69,5.53,7.83-.2,16.54.42,23.79-5.39,5.07-4.07,11.59-6.28,16.93-10.08,2.86-2.04,5.06-5.43,6.73-8.64,2.7-5.19,6.32-10.36,3.77-17.81,9.94-2.68,19.81-5.35,30.94-8.36.97,6.15,3.62,11.81,2.16,16.06-3.22,9.4-3.92,19.5-10.57,28.15-6.6,8.6-14.03,15.88-22.72,21.74-10.18,6.87-21.64,10.67-33.99,13-6.83,1.28-13.56,2.77-20.27,2.05-8.48-.92-17.08-2.56-25.11-5.38-7.22-2.53-13.99-6.67-20.46-10.87-3.84-2.5-7.21-6.12-9.92-9.87-4.7-6.51-8.84-13.44-13.01-20.32-5.22-8.62-7.56-18.17-9.94-27.92-1.39-5.69-2.92-11.26-2.06-16.83,1.15-7.49,2.87-15.06,5.63-22.1,4.19-10.7,12.28-18.41,21.11-25.75,8.28-6.88,17.54-11.3,26.97-16.07,8.38-4.24,17.47-5.38,25.88-8.77,13.79-5.56,28.46-6.05,42.77-8.24,13.97-2.14,28.24-3.19,42.37-3.13,16.48.07,32.97,1.68,49.43,2.97,12.53.98,25.04,2.25,37.5,3.88,7.05.93,13.96,2.95,20.96,4.3,4.68.9,9.43,1.49,14.15,2.18,9.94,1.46,19.9,2.78,29.81,4.41,3.43.57,6.68,2.16,10.1,2.85,7.72,1.55,15.89,1.77,23.17,4.44,12.7,4.67,26.03,5.74,38.99,8.75,7.34,1.71,14.47,4.24,21.78,6.09,8.34,2.11,16.75,3.97,25.17,5.78,7.04,1.51,14.49,1.86,21.11,4.4,9.92,3.81,20.23,4.82,30.45,6.76,8.88,1.68,17.59,4.17,26.41,6.17,3.84.87,7.75,1.42,11.63,2.08,7.03,1.2,14.04,2.57,21.11,3.54,14.38,1.97,28.79,3.72,43.19,5.56.17.02.33.05.5.06,11.76,1.18,23.52,2.41,35.28,3.52,15,1.41,30.21,1.62,44.95,4.38,17.32,3.24,34.68,4.04,52.14,4.73,15.77.62,31.53,1.49,47.3,2.06,16.28.58,32.57,1.34,48.85,1.26,14.11-.07,28.24-.91,42.31-1.96,11.02-.82,22.06-2.04,32.95-3.9,10.21-1.74,20.34-4.16,30.3-7.04,8.29-2.39,16.52-5.34,24.3-9.04,6.57-3.13,12.46-7.68,18.63-11.62,9.07-5.79,15.16-14.01,20.5-23.28,7.3-12.67,8.67-26.34,6.53-39.92-2.25-14.3-11.15-25.07-24.35-31.64-7.04-3.51-14.13-7.06-21.53-9.63-4.9-1.7-10.41-2.36-15.64-2.29-10.08.14-20.39.02-30.16,2.1-8.84,1.88-17.08,6.55-25.59,9.97-8.08,3.25-13.8,9.79-20.57,14.87-.81.6-1.08,1.9-1.66,2.83-4.39,7.07-8.93,13.93-12.7,21.51-6.84,13.75-6.68,27.83-6.08,42.16.16,3.86.5,8.81,5.2,11.33Z"/>
                <path d="M1110.9,101.82c10.91-1.41,21.09-2.73,30.58-3.96,2.37,6.61,4.19,12.2,6.37,17.64,2.21,5.51,7.41,7.52,12.88,5.35,7.07-2.81,12.21-7.3,16.88-13.71,9.59-13.13,8.68-26.24,2.21-39.47-2.57-5.26-7.35-9.79-12-13.59-6.88-5.63-14.5-10.37-21.86-15.41-9.99-6.84-21.25-7.55-32.78-6.34-6.43.68-10.79,5.28-14.93,9.54-4.9,5.04-6.93,11.8-9.92,18.07-5.27,11.05-5.46,22.6-5.81,34.01-.28,9.26,1.52,18.77,3.75,27.85,1.81,7.39,4.75,14.84,8.76,21.28,4.9,7.88,10.66,15.18,17.63,21.8,7.6,7.21,16.47,12.11,24.98,17.55,5.69,3.63,13.1,4.51,19.63,6.9,12.7,4.64,26.15,5.11,39.2,7.63,23.52,4.54,47.04,2.85,70.36,1.52,14.8-.84,29.75-2.95,44.64-4.49,4.75-.49,9.5-1.08,14.22-1.84,5.74-.93,11.44-2.08,17.15-3.16,5.81-1.1,11.59-2.42,17.43-3.31,6.68-1.03,13.45-1.52,20.13-2.58,4.12-.65,8.12-1.98,12.2-2.91,5.1-1.16,10.21-2.34,15.35-3.31,10.39-1.96,20.79-3.82,31.2-5.66,8.41-1.48,16.89-2.61,25.24-4.34,9.51-1.97,18.94-4.34,28.35-6.72,6.85-1.74,13.61-3.8,20.42-5.69,2.98-.83,5.97-1.6,8.96-2.36,10.2-2.61,20.42-5.15,30.6-7.83,7.03-1.85,14-3.91,21.01-5.81,2.21-.6,4.5-.88,6.7-1.49,6.04-1.66,12.04-3.49,18.09-5.11,7.07-1.89,14.18-3.65,21.28-5.44,4.8-1.21,9.61-2.35,14.41-3.55,4.42-1.11,8.79-2.53,13.27-3.31,6.71-1.16,13.47-1.54,19.82-4.74,3.38-1.7,7.65-1.71,11.55-2.26,6.22-.88,12.49-1.41,18.68-2.4,6.07-.97,12.07-2.32,18.09-3.55,1.28-.26,2.5-.99,3.77-1.07,23.34-1.49,46.66-3.13,70.02-4.25,10.47-.5,21.05-.58,31.48.3,13.77,1.17,27.53,3.02,41.14,5.42,10.3,1.82,20.57,4.35,30.48,7.7,8.59,2.9,16.62,7.46,24.96,11.15,4.25,1.88,8.91,3,12.9,5.31,6.25,3.61,12.16,7.84,18.1,11.97,9.48,6.59,17.62,14.33,24.56,23.82,5.32,7.27,7.75,15.05,10.54,23.22,1.67,4.87,2.88,9.53,2.11,14.23-1.28,7.78-2.78,15.73-5.78,22.95-3.07,7.37-7.02,14.97-12.48,20.62-8.17,8.45-15.77,17.96-26.96,23.03-5.58,2.53-11.38,4.66-16.75,7.58-10.27,5.57-21.44,6.62-32.65,6.55-13.07-.09-26.29-.26-39.14-2.31-10.39-1.66-20.74-5.02-29.79-11.14-2.11-1.43-5.1-1.64-7.06-3.21-6.15-4.92-11.95-10.27-17.96-15.36-5.38-4.56-6.51-11.33-9.58-17.1-4.38-8.23-3.04-16.57-2.13-24.58.75-6.56,4.02-13.19,7.51-18.99,4.47-7.44,10.26-14.08,15.9-21.61,8.27,7.44,16.12,14.5,23.26,20.92-5.25,8.47-10.67,15.88-14.49,24.04-1.64,3.51-2.59,8.76,1.53,12.81,2.2,2.17,2.84,6.07,5.19,7.92,4.64,3.67,9.86,6.66,15.05,9.56,9.26,5.18,19.38,7.09,29.92,7.57,3.15.14,6.34,2.48,9.35,2.17,9.37-.96,18.8-2.15,27.95-4.34,6.79-1.62,13.45-4.4,19.63-7.69,3.94-2.1,7.24-5.77,10.2-9.27,4.47-5.29,8.52-10.95,12.49-16.64,4.81-6.89,6.73-14.59,3.13-22.45-2.51-5.47-5.76-10.81-9.65-15.38-4.63-5.44-10.19-10.12-15.57-14.87-1.75-1.55-4.22-2.29-6.36-3.39-6.82-3.5-13.46-7.42-20.51-10.37-8.5-3.55-17.61-5.69-25.97-9.51-10.52-4.8-21.87-4.61-32.74-7.09-21.05-4.81-42.29-4.73-63.59-4.22-10.61.25-21.26.76-31.78,2.07-12.63,1.57-25.14,4.09-37.7,6.26-10.25,1.77-20.5,3.57-30.73,5.43-7.44,1.35-14.9,2.63-22.27,4.29-9.12,2.06-18.17,4.44-27.22,6.76-11.58,2.96-23.52,5.04-34.61,9.28-10.92,4.17-22.33,5.53-33.4,8.62-9.2,2.57-18.46,4.95-27.71,7.31-11.61,2.97-23.26,5.8-34.87,8.75-7.33,1.86-14.57,4.06-21.94,5.73-9.99,2.26-20.03,4.29-30.1,6.15-8.73,1.61-17.9,1.77-26.22,4.53-11.65,3.87-23.71,4.38-35.53,6.65-5.21,1-10.38,2.24-15.58,3.3-5.63,1.14-11.24,2.45-16.92,3.21-7.86,1.06-15.81,1.44-23.66,2.56-7.87,1.12-15.66,2.79-23.49,4.21-.65.12-1.31.23-1.97.29-35.04,2.91-70.14,3.37-105.24,1.89-10.01-.42-20.09-1.88-29.9-3.96-10.73-2.27-21.54-4.96-31.69-9.02-11.08-4.43-22.25-9.51-31.93-16.37-11.3-8.01-21.63-17.71-29.42-29.49-3.6-5.45-6.91-11.18-9.6-17.12-2.93-6.47-4.68-13.47-7.48-20.01-7.28-17.02-7.19-34.9-6.25-52.79.39-7.33,1.43-14.93,3.94-21.76,3.41-9.32,8.43-18.06,12.89-26.97,4.38-8.74,12.32-14.32,19.5-20.29,4.01-3.34,9.78-4.93,15.01-6.34,7.82-2.11,15.73-4.93,24.03-3.27,7.77,1.55,15.69,2.9,23.11,5.56,7.68,2.75,15.2,6.39,22.11,10.71,7.07,4.42,13.43,10.03,19.91,15.34,7.76,6.36,12.91,14.74,18.28,23.12,6.26,9.79,8.3,20.6,8.13,31.49-.13,8.45-2.31,17.26-5.46,25.17-4.05,10.2-10.97,18.68-19.69,25.85-5.95,4.9-12.62,7.63-19.34,10.92-9.46,4.63-19.34,4.14-28.41,2.08-9.66-2.19-17.5-8.65-23.6-17.56-4.81-7.02-5.29-15.23-9.24-22.3-1.31-2.34-.52-5.84-.71-9.05Z"/>
            </svg>
            <div class="bookingContent">
                <!-- Simpel datum formulier -->
                <form class="<?php echo lindenhof_bem_classes('intro-block', 'date-form'); ?>" id="sticky-date-form">
                    <div>
                        <label for="arrival-date"><?php echo esc_html__('Van', 'lindenhof'); ?></label>
                        <input
                            type="date"
                            id="arrival-date"
                            name="arrival_date"
                            required
                            min="<?php echo date('Y-m-d'); ?>"
                        />
                    </div>
                    <div>
                        <label for="departure-date"><?php echo esc_html__('Tot', 'lindenhof'); ?></label>
                        <input
                            type="date"
                            id="departure-date"
                            name="departure_date"
                            required
                            min="<?php echo date('Y-m-d'); ?>"
                        />
                    </div>
                    <button
                        type="submit"
                        class="<?php echo lindenhof_bem_classes('intro-block', 'submit-btn'); ?>"
                    >
                        <?php echo esc_html__('Start reservering', 'lindenhof'); ?>
                    </button>
                </form>
            </div>
        </div>
    <?php endif; ?>
        </div>
    </div>
</section>
