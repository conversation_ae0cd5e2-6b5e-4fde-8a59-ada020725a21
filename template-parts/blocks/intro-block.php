<?php
/**
 * Intro Block Template
 * 
 * @package Lindenhof
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get ACF fields
$title = get_field('title');
$subtitle = get_field('subtitle');
$content = get_field('content');
$background_image = get_field('background_image');
$gallery = get_field('gallery');

// Google Reviews fields
$show_google_reviews = get_field('show_google_reviews');
$google_place_id = get_field('google_place_id');

// Content sections fields
$show_faciliteiten = get_field('show_faciliteiten');
$show_eten_drinken = get_field('show_eten_drinken');
$show_omgeving = get_field('show_omgeving');
$show_algemeen = get_field('show_algemeen');

// Booking widget fields
$show_sticky_booking = get_field('show_sticky_booking');
$booking_widget_type = get_field('booking_widget_type');
$hotelhuurder_embed = get_field('hotelhuurder_embed');

// Get accommodatie info from settings
$accommodatie_locatie = lindenhof_get_setting('accommodatie_locatie');
$accommodatie_personen = lindenhof_get_setting('accommodatie_personen');
$accommodatie_slaapplaatsen = lindenhof_get_setting('accommodatie_slaapplaatsen');

// Get Google Reviews data if needed
$reviews_data = null;
if ($show_google_reviews && $google_place_id) {
    $reviews_data = lindenhof_get_google_reviews($google_place_id);
    // Check if it's a WP_Error and set to null if so
    if (is_wp_error($reviews_data)) {
        $reviews_data = null;
    }
}

// Generate unique ID for this block instance
$block_id = $block_id ?? lindenhof_unique_id('introBlock');

// Build CSS classes
$css_classes = lindenhof_bem_classes('introBlock', '', '', array('lindenhof-block'));

// Build inline styles for background image
$inline_styles = '';
if ($background_image && !empty($background_image['url'])) {
    $inline_styles = sprintf(
        'style="background-image: url(%s);"',
        esc_url($background_image['url'])
    );
}
?>

<section class="introBlock" data-anchor="home" data-init>
    <div class="contentWrapper">
        <?php if ($gallery && is_array($gallery)): ?>
            <div class="backgroundGallery">
                 <?php $gallery = array_slice($gallery, 0, 3); ?>
                  <?php $gallery = array_reverse($gallery); ?>
                <?php foreach ($gallery as $image): ?>
                     <?php if ($image == $gallery[0]): ?>
                        <div class="innerCol">
                            <div class="galleryItem" data-gallery data-image="<?php echo esc_url($image['url']); ?>">
                                <?php echo lindenhof_safe_image($image, 'large', lindenhof_bem_classes('intro-block', 'gallery-image')); ?>
                            </div>
                        </div>
                        <div class="innerCol">
                    <?php else: ?>
                            <div class="galleryItem" data-gallery data-image="<?php echo esc_url($image['url']); ?>">
                                <?php echo lindenhof_safe_image($image, 'medium_large', lindenhof_bem_classes('intro-block', 'gallery-image')); ?>
                            </div>
                    <?php endif; ?>
                    
                <?php endforeach; ?>
            </div>
            <div class="arrows">
                <div class="arrow prev">
                    <i class="icon-chevron-left"></i>
                </div>
                <div class="arrow next">
                    <i class="icon-chevron-right"></i>
                </div>
            </div>
        </div>
        <?php endif; ?>
        <div class="cols">
        <div class="col">
            <div class="innerCol">
                <div class="logo">
                     <img src="<?php echo esc_url(lindenhof_get_logo_url()); ?>" alt="Logo | <?php echo get_bloginfo('name'); ?>">
                </div>
            </div>
        <?php if ($title): ?>
            <div class="innerCol">
                <div class="headerWrapper">
                    <div class="headerInfo">
                        <?php if ($show_google_reviews && $google_place_id && $reviews_data && isset($reviews_data['rating'])): ?>
                            <div class="googleReviews">
                                <?php echo lindenhof_render_google_stars($reviews_data['rating']); ?>
                                <span class="rating">
                                    <?php echo esc_html($reviews_data['rating']); ?>
                                </span>
                                <span class="totalRatings">
                                    (<?php echo esc_html($reviews_data['total_ratings'] ?? 0); ?>)
                                </span>
                            </div>
                        <?php endif; ?>
                            <div class="specs">
                        <?php if ($accommodatie_locatie): ?>
                            <div class="spec">
                                <i class="icon-location"></i>
                                <span>
                                    <?php echo esc_html($accommodatie_locatie); ?>
                                </span>
                            </div>
                        <?php endif; ?>
                            |
                        <?php if ($accommodatie_personen): ?>
                            <div class="spec">
                                <i class="icon-person"></i>
                                <span>
                                    <?php echo sprintf(__('%d', 'lindenhof'), $accommodatie_personen); ?>
                                </span>
                            </div>
                        <?php endif; ?>
                            |
                        <?php if ($accommodatie_slaapplaatsen): ?>
                            <div class="spec">
                                <i class="icon-bed"></i>
                                <span>
                                    <?php echo sprintf(__('%d', 'lindenhof'), $accommodatie_slaapplaatsen); ?>
                                </span>
                            </div>
                        <?php endif; ?>
                        </div>
                    </div>

                    <h1 class="title bold" data-lines data-letters>
                        <?php echo lindenhof_safe_text($title); ?>
                    </h1>

                    <div class="text">
                        <?php echo html_entity_decode($content); ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="divider"></div>
         <div class="innerCol">
            <!-- algemeen -->
             <?php if ($show_algemeen): ?>
                <div class="algemeen">
                    <?php echo get_template_part('template-parts/blocks/algemeen-block'); ?>
                </div>
            <?php endif; ?>
        </div>
        <div class="divider"></div>
        <div class="innerCol">
            <!-- faciliteiten -->
             <?php if ($show_faciliteiten): ?>
                <div class="faciliteiten">
                    <?php echo get_template_part('template-parts/blocks/faciliteiten-block'); ?>
                </div>
            <?php endif; ?>
        </div>
        <div class="divider"></div>
        <div class="innerCol">
            <!-- eten & drinken -->
             <?php if ($show_eten_drinken): ?>
                <div class="etenDrinken">
                    <?php echo get_template_part('template-parts/blocks/eten-drinken-block'); ?>
                </div>
            <?php endif; ?>
        </div>
        <div class="divider"></div>
        <div class="innerCol">
            <!-- omgeving -->
             <?php if ($show_omgeving): ?>
                <div class="omgeving">
                    <?php echo get_template_part('template-parts/blocks/omgeving-block'); ?>
                </div>
            <?php endif; ?>
        </div>
        <div class="divider"></div>
    </div>
    <div class="col">
    <?php if ($show_sticky_booking): ?>
        <div class="stickyBooking" data-widget-type="<?php echo esc_attr($booking_widget_type); ?>">
            <!-- raaf.gif include (in img) -->
            <img src="<?php echo get_template_directory_uri(); ?>/assets/img/raaf.gif" alt="Raaf" class="crow">
            <?php if ($booking_widget_type === 'hotelhuurder' && $hotelhuurder_embed): ?>
                <div class="bookingWidget">
                    <?php echo $hotelhuurder_embed; // Already sanitized by ACF ?>
                </div>

            <?php elseif ($booking_widget_type === 'huurkalender'): ?>
                <div class="<?php echo lindenhof_bem_classes('intro-block', 'booking-widget'); ?>">
                    <div class="<?php echo lindenhof_bem_classes('intro-block', 'booking-header'); ?>">
                        <h4 class="<?php echo lindenhof_bem_classes('intro-block', 'booking-title'); ?>">
                            <?php echo esc_html__('Direct boeken', 'lindenhof'); ?>
                        </h4>
                        <button class="<?php echo lindenhof_bem_classes('intro-block', 'booking-close'); ?>" aria-label="<?php echo esc_attr__('Sluiten', 'lindenhof'); ?>">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="<?php echo lindenhof_bem_classes('intro-block', 'booking-content'); ?>">
                        <!-- Simpel datum formulier -->
                        <form class="<?php echo lindenhof_bem_classes('intro-block', 'date-form'); ?>" id="sticky-date-form">
                            <div class="<?php echo lindenhof_bem_classes('intro-block', 'date-inputs'); ?>">
                                <div class="<?php echo lindenhof_bem_classes('intro-block', 'date-field'); ?>">
                                    <label for="arrival-date"><?php echo esc_html__('Van', 'lindenhof'); ?></label>
                                    <input
                                        type="date"
                                        id="arrival-date"
                                        name="arrival_date"
                                        required
                                        min="<?php echo date('Y-m-d'); ?>"
                                    />
                                </div>
                                <div class="<?php echo lindenhof_bem_classes('intro-block', 'date-field'); ?>">
                                    <label for="departure-date"><?php echo esc_html__('Tot', 'lindenhof'); ?></label>
                                    <input
                                        type="date"
                                        id="departure-date"
                                        name="departure_date"
                                        required
                                        min="<?php echo date('Y-m-d'); ?>"
                                    />
                                </div>
                            </div>
                            <button
                                type="submit"
                                class="<?php echo lindenhof_bem_classes('intro-block', 'submit-btn'); ?>"
                            >
                                <?php echo esc_html__('Start reservering', 'lindenhof'); ?>
                            </button>
                        </form>
                    </div>
                </div>

            <?php endif; ?>

            <button class="<?php echo lindenhof_bem_classes('intro-block', 'booking-toggle'); ?>" aria-label="<?php echo esc_attr__('Booking widget openen', 'lindenhof'); ?>">
                <span class="<?php echo lindenhof_bem_classes('intro-block', 'booking-icon'); ?>">📅</span>
                <span class="<?php echo lindenhof_bem_classes('intro-block', 'booking-text'); ?>">
                    <?php echo esc_html__('Boek nu', 'lindenhof'); ?>
                </span>
            </button>

        </div>
    <?php endif; ?>
        </div>
    </div>
</section>
