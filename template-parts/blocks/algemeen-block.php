<?php
/**
 * Algemeen Block Template
 * 
 * @package Lindenhof
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get global content from settings if enabled
    $global_title = get_option('lindenhof_algemene_info_titel', '');
    $global_text = get_option('lindenhof_algemene_info_tekst', '');
    $global_image_id = get_option('lindenhof_algemene_info_afbeelding', '');

    // Use global content if available, otherwise fall back to custom content
    if (!empty($global_title)) {
        $title = $global_title;
    }
    if (!empty($global_text)) {
        $content = $global_text;
    }
    if (!empty($global_image_id)) {
        $image = array(
            'ID' => $global_image_id,
            'url' => wp_get_attachment_image_url($global_image_id, 'large'),
            'alt' => get_post_meta($global_image_id, '_wp_attachment_image_alt', true)
        );
    }

// Generate unique ID for this block instance
$block_id = $block_id ?? lindenhof_unique_id('algemeen-block');

?>
<div class="algemeenBlock" data-section>
     <?php if ($title): ?>
        <h2 class="title" data-letters>
            <?php echo lindenhof_safe_text($title); ?>
        </h2>
    <?php endif; ?>
    <div class="innerCols">
        <div class="innerCol">
        <?php if ($image): ?>
                <div class="imageWrapper">
                    <img data-src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" class="lazy">
                </div>
            <?php endif; ?>
        </div>
        <div class="innerCol">
            <?php if ($content): ?>
                <div class="text">
                    <?php echo wpautop(lindenhof_safe_text($content, true)); ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>