<?php
// Get all faciliteiten posts to display
$faciliteiten_posts = get_posts(array(
    'post_type' => 'faciliteiten',
    'numberposts' => -1,
    'post_status' => 'publish',
    'orderby' => 'menu_order title',
    'order' => 'ASC'
));

// Get block title if this is used as a block
$block_title = get_field('title') ?: 'Faciliteiten';
?>

<div class="faciliteitenBlock">
        <?php if ($block_title): ?>
            <h2 class="title">Faciliteiten</h2>
        <?php endif; ?>

        <?php if (!empty($faciliteiten_posts)): ?>
            <div class="faciliteiten">
                <?php foreach ($faciliteiten_posts as $post): ?>
                    <?php setup_postdata($post); ?>

                    <div class="faciliteit">
                        <h3 class="textTitle"><?php echo esc_html(get_the_title()); ?></h3>

                        <?php
                        $ja_groep = get_field('ja_groep', $post->ID);
                        $nee_groep = get_field('nee_groep', $post->ID);
                        ?>

                        <ul>
                            <?php if ($ja_groep): ?>
                                <?php foreach ($ja_groep as $item): ?>
                                    <li class="yes">
                                        <span class="icon">✓</span>
                                        <?php echo esc_html($item['tekst']); ?>
                                    </li>
                                <?php endforeach; ?>
                            <?php endif; ?>

                            <?php if ($nee_groep): ?>
                                <?php foreach ($nee_groep as $item): ?>
                                    <li class="no">
                                        <span class="icon">✗</span>
                                        <?php echo esc_html($item['tekst']); ?>
                                    </li>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </ul>
                    </div>

                <?php endforeach; ?>
                <?php wp_reset_postdata(); ?>
            </div>
        <?php else: ?>
            <p>Geen faciliteiten gevonden. <a href="<?php echo admin_url('post-new.php?post_type=faciliteiten'); ?>">Voeg faciliteiten toe</a> in het admin panel.</p>
        <?php endif; ?>

        </div>
