// out: false
.introBlock {
    .backgroundGallery {
        width: 100%;
        height: @vw100 * 6;
        position: relative;
        display: block;
        cursor: pointer;
        &:hover {
            .galleryItem {
                img {
                    opacity: .8;
                }
            }
        }
        * {
            cursor: pointer;
        }
        .arrows {
            position: absolute;
            top: 50%;
            .transform(translateY(-50%));
            width: 100%;
            left: 0;
            .arrow {
                height: @vw50;
                width: @vw50;
                position: absolute;
                top: 50%;
                .rounded(50%);
                background: @almostWhite;
                line-height: @vw50;
                text-align: center;
                &.prev {
                    .transform(translate(-50%, -50%));
                    left: 0;
                }
                &.next {
                    .transform(translate(50%, -50%));
                    right: 0;
                }
            }
        }
        .innerCol {
            display: inline-block;
            height: 100%;
            vertical-align: top;
            &:nth-child(1) {
                width: calc(50% ~"+" @vw106 ~"+" @vw16);
                &:has(.faciliteiten) {
                    width: 100%;
                }
                .galleryItem {
                    height: 100%;
                }
            }
            &:nth-child(2) {
                padding-left: @vw16;
                width: calc(50% ~"- (" @vw106 ~"+" @vw16 ~")");
                display: inline-flex;
                flex-direction: column;
                justify-content: space-between;
                .galleryItem {
                    height: 50%;
                    &:first-child {
                        margin-bottom: @vw16;
                    }
                }
            }
        }
        .galleryItem {
            .rounded(@vw5);
            overflow: hidden;
            position: relative;
            width: 100%;
            display: inline-block;
            &:hover {
                img {
                    opacity: 1;
                }
            }
            &:after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(@almostBlack, 0.3);
            }
            img {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center;
                .transitionMore(opacity, .45s);
            }
        }
    }
    .logo {
        width: 100%;
        height: 0;
        .paddingRatio(1,1);
        .transform(translateY(-30%));
        background: @almostBlack;
        .rounded(50%);
        img {
            position: absolute;
            width: 80%;
            height: auto;
            left: 50%;
            top: 50%;
            .transform(translate(-50%, -50%));
            object-fit: contain;
            object-position: center;
        }
    }
    .headerWrapper {
        .specs {
            display: flex;
            font-size: @vw14;
            color: rgba(@almostBlack, .6);
            gap: @vw16;
        }
        .title {
            margin: @vw14 0 @vw22 0;
        }
    }
    .cols {
        display: flex;
        padding-right: (@vw106 + @vw16);
        .transform(translateY(-@vw60));
        &:before {
            content: '';
            position: absolute;
            top: -@vw40;
            right: @vw106;
            width: 80%;
            height: @vw100 * 2;
            background: @almostWhite;
            opacity: 1;
            pointer-events: none;
            .rounded(@vw5);
            box-shadow: -@vw60 -@vw60 @vw60 rgba(0,0,0,.6);
        }
        .col {
            display: inline-block;
            vertical-align: top;
            &:nth-child(1) {
                padding-right: @vw50;
                width: calc(100% ~"- (" @vw106 ~"*" 3 ~"+" @vw16 ~"*" 3 ~")");
                .innerCol {
                    display: inline-block;
                    position: relative;
                    vertical-align: top;
                    &:first-child {
                        padding-right: @vw30;
                        width: (@vw106 * 3) + (@vw16 * 3);
                    }
                    &:nth-child(2) {
                        padding-left: @vw16;
                        width: calc(100% ~"- (" @vw106 ~"*" 3 ~"+" @vw16 ~"*" 3 ~")");
                    }
                }
            }
            &:nth-child(2) {
                width: (@vw106 * 3) + (@vw16 * 3);
            }
        }
    }
    .divider {
        margin: @vw100 + @vw40 0;
        border: 1px dashed @lightGrey;
    }

    // Booking form styling
    &__date-form {
        padding: @vw20;

        .intro-block__date-inputs {
            display: flex;
            gap: @vw16;
            margin-bottom: @vw20;

            .intro-block__date-field {
                flex: 1;

                label {
                    display: block;
                    font-size: @vw14;
                    font-weight: 600;
                    color: @almostBlack;
                    margin-bottom: @vw8;
                }

                input[type="date"] {
                    width: 100%;
                    padding: @vw12 @vw16;
                    border: 2px solid @lightGrey;
                    .rounded(@vw8);
                    font-size: @vw16;
                    font-family: inherit;
                    background: @almostWhite;
                    .transition(border-color, 0.3s);

                    &:focus {
                        outline: none;
                        border-color: @almostBlack;
                    }

                    &:invalid {
                        border-color: #e74c3c;
                    }
                }
            }
        }

        .intro-block__submit-btn {
            width: 100%;
            padding: @vw16 @vw24;
            background: @almostBlack;
            color: @almostWhite;
            border: none;
            .rounded(@vw8);
            font-size: @vw16;
            font-weight: 600;
            font-family: inherit;
            cursor: pointer;
            .transition(all, 0.3s);

            &:hover {
                background: lighten(@almostBlack, 10%);
                .transform(translateY(-2px));
            }

            &:active {
                .transform(translateY(0));
            }
        }

        // Mobile responsive
        @media (max-width: 768px) {
            .intro-block__date-inputs {
                flex-direction: column;
                gap: @vw12;
            }
        }
    }
    .stickyBooking {
        position: absolute;
        top: 0;
        background: @almostWhite;
        left: 0;
        width: 100%;
        .crow {
            position: relative;
            top: 0;
            width: @vw70;
            margin: @vw10 auto @vw5 auto;
            display: block;
            mix-blend-mode: difference;
        }
        svg {
            height: @vw14;
            width: auto;
            object-fit: contain;
            margin: auto;
            display: block;
        }
        form {
            display: flex;
            flex-direction: column;
            margin-top: @vw24;
            gap: @vw16;
        }
        label {
            display: inline-block;
            margin-bottom: @vw4;
        }
        input {
            width: 100%;
            padding: 0.8rem 1rem;
            background: transparent;
            border-color: rgba(@almostBlack, .1);
            .rounded(@vw10);
            color: @almostBlack;
            font-size: @vw18;
            font-family: 'Inter', sans-serif;
            outline: none;
            .transition(.3s);
            &:focus {
                border-color: rgba(@primaryColor, .4);
            }
        }
        button {
            width: 100%;
            padding: 0.8rem 1rem;
            background: @primaryColor;
            border: none;
            .rounded(@vw10);
            color: @almostWhite;
            font-size: @vw18;
            font-family: 'Inter', sans-serif;
            outline: none;
            .transition(.3s);
            box-shadow: none;
        }
    }

    // Booking form styling
    &__date-form {
        padding: @vw20;

        .intro-block__date-inputs {
            display: flex;
            gap: @vw16;
            margin-bottom: @vw20;

            .intro-block__date-field {
                flex: 1;

                label {
                    display: block;
                    font-size: @vw14;
                    font-weight: 600;
                    color: @almostBlack;
                    margin-bottom: @vw8;
                }

                input[type="date"] {
                    width: 100%;
                    padding: @vw12 @vw16;
                    border: 2px solid @lightGrey;
                    .rounded(@vw8);
                    font-size: @vw16;
                    font-family: inherit;
                    background: @almostWhite;
                    .transition(border-color, 0.3s);

                    &:focus {
                        outline: none;
                        border-color: @almostBlack;
                    }

                    &:invalid {
                        border-color: #e74c3c;
                    }
                }
            }
        }

        .intro-block__submit-btn {
            width: 100%;
            padding: @vw16 @vw24;
            background: @almostBlack;
            color: @almostWhite;
            border: none;
            .rounded(@vw8);
            font-size: @vw16;
            font-weight: 600;
            font-family: inherit;
            cursor: pointer;
            .transition(all, 0.3s);

            &:hover {
                background: lighten(@almostBlack, 10%);
                .transform(translateY(-2px));
            }

            &:active {
                .transform(translateY(0));
            }
        }

        // Mobile responsive
        @media (max-width: 768px) {
            .intro-block__date-inputs {
                flex-direction: column;
                gap: @vw12;
            }
        }
    }
    .algemeenBlock {
        .title {
            margin-bottom: @vw24;
        }
        .imageWrapper {
            width: 100%;
            height: 0;
            position: relative;
            overflow: hidden;
            .rounded(@vw5);
            .paddingRatio(1,1);
            video, img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                position: absolute;
                top: 0;
                left: 0;
                object-position: center;
            }
        }
    }
    .faciliteitenBlock {
        .title {
            margin-bottom: @vw24;
        }
        .textTitle {
            margin-bottom: @vw8;
        }
        ul {
            list-style: none;
            padding: 0;
            margin: 0;
            color: rgba(@almostBlack, .7);
        }
        .faciliteiten {
            display: block;
            margin-left: -@vw8;
            width: calc(100% ~"+" @vw16);
            .faciliteit {
                display: inline-block;
                vertical-align: top;
                margin: 0 @vw8;
                width: calc(33.3333% ~"-" @vw16);
            }
        }
    }
}