<?php
/**
 * Contact Block Template
 * 
 * @package Lindenhof
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get ACF fields
$title = get_field('title');
$content = get_field('content');
$show_form = get_field('show_form');
$form_shortcode = get_field('form_shortcode');
$show_info = get_field('show_info');
$show_map = get_field('show_map');
$map_embed = get_field('map_embed');

// Get contact info from theme settings
$contact_email = lindenhof_get_setting('contact_email');
$contact_phone = lindenhof_get_setting('contact_phone');
$contact_address = lindenhof_get_setting('contact_address');
$social_facebook = lindenhof_get_setting('social_facebook');
$social_instagram = lindenhof_get_setting('social_instagram');
$social_youtube = lindenhof_get_setting('social_youtube');

// Generate unique ID for this block instance
$block_id = $block_id ?? lindenhof_unique_id('contact-block');

// Build CSS classes
$css_classes = lindenhof_bem_classes('contact-block', '', '', array('lindenhof-block'));
?>

<section 
    id="<?php echo esc_attr($block_id); ?>" 
    class="<?php echo esc_attr($css_classes); ?>"
    <?php echo lindenhof_swup_attributes(); ?>
>
    <div class="<?php echo lindenhof_bem_classes('contact-block', 'container'); ?>">
        
        <?php if ($title): ?>
            <h2 class="title" data-lines data-letters>
                <?php echo lindenhof_safe_text($title); ?>
            </h2>
        <?php endif; ?>
        
        <?php if ($content): ?>
            <div class="<?php echo lindenhof_bem_classes('contact-block', 'content'); ?>">
                <?php echo lindenhof_safe_text($content, true); ?>
            </div>
        <?php endif; ?>
        
        <div class="<?php echo lindenhof_bem_classes('contact-block', 'wrapper'); ?>">
            
            <?php if ($show_info && ($contact_email || $contact_phone || $contact_address)): ?>
                <div class="<?php echo lindenhof_bem_classes('contact-block', 'info'); ?>">
                    
                    <h3 class="<?php echo lindenhof_bem_classes('contact-block', 'info-title'); ?>">
                        <?php echo esc_html__('Contact informatie', 'lindenhof'); ?>
                    </h3>
                    
                    <div class="<?php echo lindenhof_bem_classes('contact-block', 'info-list'); ?>">
                        
                        <?php if ($contact_email): ?>
                            <div class="<?php echo lindenhof_bem_classes('contact-block', 'info-item'); ?>">
                                <span class="<?php echo lindenhof_bem_classes('contact-block', 'info-label'); ?>">
                                    <?php echo esc_html__('E-mail:', 'lindenhof'); ?>
                                </span>
                                <a 
                                    href="mailto:<?php echo lindenhof_safe_url($contact_email); ?>"
                                    class="<?php echo lindenhof_bem_classes('contact-block', 'info-link'); ?>"
                                >
                                    <?php echo lindenhof_safe_text($contact_email); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($contact_phone): ?>
                            <div class="<?php echo lindenhof_bem_classes('contact-block', 'info-item'); ?>">
                                <span class="<?php echo lindenhof_bem_classes('contact-block', 'info-label'); ?>">
                                    <?php echo esc_html__('Telefoon:', 'lindenhof'); ?>
                                </span>
                                <a 
                                    href="tel:<?php echo lindenhof_safe_url($contact_phone); ?>"
                                    class="<?php echo lindenhof_bem_classes('contact-block', 'info-link'); ?>"
                                >
                                    <?php echo lindenhof_safe_text($contact_phone); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($contact_address): ?>
                            <div class="<?php echo lindenhof_bem_classes('contact-block', 'info-item'); ?>">
                                <span class="<?php echo lindenhof_bem_classes('contact-block', 'info-label'); ?>">
                                    <?php echo esc_html__('Adres:', 'lindenhof'); ?>
                                </span>
                                <address class="<?php echo lindenhof_bem_classes('contact-block', 'info-address'); ?>">
                                    <?php echo lindenhof_safe_text($contact_address); ?>
                                </address>
                            </div>
                        <?php endif; ?>
                        
                    </div>
                    
                    <?php if ($social_facebook || $social_instagram || $social_youtube): ?>
                        <div class="<?php echo lindenhof_bem_classes('contact-block', 'social'); ?>">
                            
                            <h4 class="<?php echo lindenhof_bem_classes('contact-block', 'social-title'); ?>">
                                <?php echo esc_html__('Volg ons', 'lindenhof'); ?>
                            </h4>
                            
                            <div class="<?php echo lindenhof_bem_classes('contact-block', 'social-links'); ?>">
                                
                                <?php if ($social_facebook): ?>
                                    <a 
                                        href="<?php echo lindenhof_safe_url($social_facebook); ?>"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        class="<?php echo lindenhof_bem_classes('contact-block', 'social-link', 'facebook'); ?>"
                                        aria-label="<?php echo esc_attr__('Facebook', 'lindenhof'); ?>"
                                    >
                                        <?php echo esc_html__('Facebook', 'lindenhof'); ?>
                                    </a>
                                <?php endif; ?>
                                
                                <?php if ($social_instagram): ?>
                                    <a 
                                        href="<?php echo lindenhof_safe_url($social_instagram); ?>"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        class="<?php echo lindenhof_bem_classes('contact-block', 'social-link', 'instagram'); ?>"
                                        aria-label="<?php echo esc_attr__('Instagram', 'lindenhof'); ?>"
                                    >
                                        <?php echo esc_html__('Instagram', 'lindenhof'); ?>
                                    </a>
                                <?php endif; ?>
                                
                                <?php if ($social_youtube): ?>
                                    <a 
                                        href="<?php echo lindenhof_safe_url($social_youtube); ?>"
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        class="<?php echo lindenhof_bem_classes('contact-block', 'social-link', 'youtube'); ?>"
                                        aria-label="<?php echo esc_attr__('YouTube', 'lindenhof'); ?>"
                                    >
                                        <?php echo esc_html__('YouTube', 'lindenhof'); ?>
                                    </a>
                                <?php endif; ?>
                                
                            </div>
                            
                        </div>
                    <?php endif; ?>
                    
                </div>
            <?php endif; ?>
            
            <?php if ($show_form && $form_shortcode): ?>
                <div class="<?php echo lindenhof_bem_classes('contact-block', 'form'); ?>">
                    
                    <h3 class="<?php echo lindenhof_bem_classes('contact-block', 'form-title'); ?>">
                        <?php echo esc_html__('Stuur ons een bericht', 'lindenhof'); ?>
                    </h3>
                    
                    <div class="<?php echo lindenhof_bem_classes('contact-block', 'form-wrapper'); ?>">
                        <?php echo do_shortcode($form_shortcode); ?>
                    </div>
                    
                </div>
            <?php endif; ?>
            
        </div>
        
        <?php if ($show_map && $map_embed): ?>
            <div class="<?php echo lindenhof_bem_classes('contact-block', 'map'); ?>">
                
                <h3 class="<?php echo lindenhof_bem_classes('contact-block', 'map-title'); ?>">
                    <?php echo esc_html__('Locatie', 'lindenhof'); ?>
                </h3>
                
                <div class="<?php echo lindenhof_bem_classes('contact-block', 'map-wrapper'); ?>">
                    <?php echo wp_kses($map_embed, array(
                        'iframe' => array(
                            'src' => array(),
                            'width' => array(),
                            'height' => array(),
                            'style' => array(),
                            'allowfullscreen' => array(),
                            'loading' => array(),
                            'referrerpolicy' => array(),
                        ),
                    )); ?>
                </div>
                
            </div>
        <?php endif; ?>
        
    </div>
</section>
